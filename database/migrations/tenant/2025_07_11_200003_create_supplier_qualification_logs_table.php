<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('supplier_qualification_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_qualification_id')->constrained('supplier_qualifications');
            $table->foreignId('operator_id')->constrained('operators');
            $table->enum('action', ['created', 'deleted']);
            $table->json('data')->nullable(); // Store qualification data for audit purposes
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('supplier_qualification_logs');
    }
};
