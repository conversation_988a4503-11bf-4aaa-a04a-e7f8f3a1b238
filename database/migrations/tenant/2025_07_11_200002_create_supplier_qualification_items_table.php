<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('supplier_qualification_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_qualification_id')->constrained();
            $table->foreignId('supplier_qualification_criterion_id');
            $table->integer('sequence');
            $table->decimal('grade');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('supplier_qualification_criterion_id', 'sqi_sqci_foreign_id')
                ->references('id')
                ->on('supplier_qualification_criteria');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('supplier_qualification_items');
    }
};
