<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('provider_qualification_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('provider_qualification_id')->constrained();
            $table->foreignId('provider_qualification_criterion_id');
            $table->integer('sequence');
            $table->decimal('grade');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('provider_qualification_criterion_id', 'pqi_pqci_foreign_id')
                ->references('id')
                ->on('provider_qualification_criteria');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('provider_qualification_items');
    }
};
