@props([
    'resourceRoute',
    'main' => true,
    'importsResource' => false,
    'exportsResource' => false,
    'createsResource' => false,
    'createRoute' => null,
    'actions' => true,
    'backButton' => false,
    'backRoute' => null,
    'setups' => false,
])

<div class="{{ $main ? 'px-4' : 'container' }}">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card-header d-flex align-items-center" {{ $attributes }}>
                <strong @if ($main) class="card-header-title" @endif>{{ __("$resourceRoute.cards.index.header.title") }}</strong>
                <div class="d-flex ml-auto">
                    @if ($setups)
                        <button class="btn btn-outline-secondary @if (!$main) btn-sm @endif dropdown-toggle mr-2" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-cog mr-1"></i> Configurações
                        </button>
                        <div class="dropdown-menu dropdown-menu-right shadow-sm" aria-labelledby="dropdownMenuButton">
                            {{ $setupLinks ?? ''}}
                        </div>
                    @endif
                    @if ($actions)
                    <div class="dropdown ml-2">
                        <button class="btn btn-outline-secondary @if (!$main) btn-sm @endif dropdown-toggle mr-2" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-clipboard-check mr-1"></i> Ações
                        </button>
                        <div class="dropdown-menu dropdown-menu-right shadow-sm" aria-labelledby="dropdownMenuButton">
                            {{ $actionLinks ?? ''}}
                            @if ($importsResource)<button class="dropdown-item" data-toggle="modal" data-target="#import-modal">Importar</button>@endif
                            @if ($exportsResource)<button class="dropdown-item" data-toggle="modal" data-target="#export-modal">Exportar</button>@endif
                        </div>
                    </div>
                    @endif
                    @if ($createsResource)
                        <x-button.create :route='$createRoute ?? route("$resourceRoute.create")' :small="!$main" />
                    @endif
                    @if ($backButton)
                    <div class="ml-2">
                        <x-button.back :route="$backRoute ?? url()->previous()" />
                    </div>
                    @endif
                </div>
            </div>

            {{ $filters ?? '' }}

            <x-index-card.body>
                {{ $slot }}
            </x-index-card.body>
        </div>
    </div>
</div>
