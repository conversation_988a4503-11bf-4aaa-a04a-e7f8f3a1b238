<div class="base-container-sidebar" style="display: none;">
    <div class="base-container-sidebar-upper">
        <div class="fa fa-2x d-flex justify-content-end" onclick="toggleMenu()" style="cursor: pointer;">
            &times;
        </div>
        <div class="base-container-sidebar-upper-logo">
            <img class="d-inline-flex ctm-img-nav" src="{{ env('DIGITALOCEAN_SPACES_IMAGES_URL') . 'logo-' . tenant('id') . '.png' }}" alt="Logo"/>
        </div>
    </div>
    <div class="px-3">
        <hr>
    </div>
    <div class="base-container-sidebar-lower">
        {{-- @if (!is_null(auth()->user()->email_verified_at)) --}}
        <a class="d-flex align-items-center @if (request()->routeIs('home')) base-container-sidebar-lower-item-selected @else base-container-sidebar-lower-item @endif" href="{{ route('home') }}">
            <i class="fas fa-tachometer-alt base-container-sidebar-lower-item-icon"></i>
            <span class="base-container-sidebar-lower-item-link">Início</span>
        </a>
        <hr>
        <ul class="base-container-sidebar-lower-menu">
            @if (auth()->user()->viewsAnyManagementModule())
                <li class="module-item" onclick="openDropdown('management')">
                    <x-sidebar.module-item :icon="'fas fa-edit'" :module="\App\Core\Module::MANAGEMENT" />
                    <ul id="management-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_SCHEDULES))<li><x-sidebar.link-item :entity="'schedules'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CATEGORIES))<li><x-sidebar.link-item :entity="'categories'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CHECKLISTS))<li><x-sidebar.link-item :entity="'checklists'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_COMPANIES))<li><x-sidebar.link-item :entity="'companies'" /></li>@endif
                        <x-sidebar.link-item :entity="'cnaes'"></x-sidebar.link-item>
                        @if (auth()->user()->can(\App\Models\Permission::GET_TEAMS))<li><x-sidebar.link-item :entity="'teams'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::CREATE_COMPANIES))<li><x-sidebar.link-item :entity="'soc_company_environments'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::UPDATE_PROCEDURES))<li><x-sidebar.link-item :entity="'equivalences'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_HOLIDAYS))<li><x-sidebar.link-item :entity="'holidays'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_COMPANY_GROUPS))<li><x-sidebar.link-item :entity="'company_groups'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_INDICES))<li><x-sidebar.link-item :entity="'indices'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_REASONS))<li><x-sidebar.link-item :entity="'reasons'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_OPERATORS))<li><x-sidebar.link-item :entity="'operators'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_ROLES))<li><x-sidebar.link-item :entity="'roles'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_PRE_COMPANIES))<li><x-sidebar.link-item :entity="'pre_companies'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_PROCEDURES))<li><x-sidebar.link-item :entity="'procedures'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_COMMISSION_RULES))<li><x-sidebar.link-item :entity="'commission_rules'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CONTRACT_TYPES))<li><x-sidebar.link-item :entity="'contract_types'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_EXPENSE_TYPES))<li><x-sidebar.link-item :entity="'expense_types'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_WORKFLOWS))<li><x-sidebar.link-item :entity="'workflows'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyContractModule())
                <li class="module-item" onclick="openDropdown('contracts')">
                    <x-sidebar.module-item :icon="'fas fa-file-alt'" :module="\App\Core\Module::CONTRACT" />
                    <ul id="contracts-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_CONTRACTS))<li><x-sidebar.link-item :entity="'contracts'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CONTRACT_BILLING_GROUPS))<li><x-sidebar.link-item :entity="'contract_billing_groups'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CONTRACTS))<li><x-sidebar.link-item :entity="'contract_readjustments'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::CREATE_SERVICE_ORDERS))<li><x-sidebar.link-item :entity="'service_orders_renewal_panel'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyAccreditationModule())
                <li class="module-item" onclick="openDropdown('accreditation')">
                    <x-sidebar.module-item :icon="'fas fa-globe'" :module="\App\Core\Module::ACCREDITATION" />
                    <ul id="accreditation-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_PROVIDERS))<li><x-sidebar.link-item :entity="'providers'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_PROVIDER_EXPENSES))<li><x-sidebar.link-item :entity="'provider_expenses'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_PROVIDER_QUALIFICATIONS))<li><x-sidebar.link-item :entity="'provider_qualifications'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyProvidingModule())
                <li class="module-item" onclick="openDropdown('providing')">
                    <x-sidebar.module-item :icon="'fas fa-edit'" :module="\App\Core\Module::PROVIDING" />
                    <ul id="providing-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_SUPPLIERS))<li><x-sidebar.link-item :entity="'suppliers'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_SUPPLIER_EXPENSES))<li><x-sidebar.link-item :entity="'supplier_expenses'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_SUPPLIER_QUALIFICATIONS))<li><x-sidebar.link-item :entity="'supplier_qualifications'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyCrmModule())
                <li class="module-item" onclick="openDropdown('crm')">
                    <x-sidebar.module-item :icon="'fas fa-handshake'" :module="\App\Core\Module::CRM" />
                    <ul id="crm-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_CRM_CITY_COVERAGES))<li><x-sidebar.link-item :entity="'crm_city_coverages'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CRM_CONVERSIONS))<li><x-sidebar.link-item :entity="'crm_conversions'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_LEAD_COMPANIES))<li><x-sidebar.link-item :entity="'lead_companies'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CRM_FUNNELS))<li><x-sidebar.link-item :entity="'crm_funnels'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CRM_ORIGINS))<li><x-sidebar.link-item :entity="'crm_origins'" /></li>@endif
                        <li><x-sidebar.link-item :entity="'crm_indicators'" /></li>
                        @if (auth()->user()->can(\App\Models\Permission::GET_LEADS))<li><x-sidebar.link-item :entity="'crm_funnels_panel'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_CRM_FUNNEL_TYPES))<li><x-sidebar.link-item :entity="'crm_funnel_types'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyAfterSalesModule())
                <li class="module-item" onclick="openDropdown('after_sales')">
                    <x-sidebar.module-item :icon="'fas fa-handshake'" :module="\App\Core\Module::AFTER_SALES" />
                    <ul id="after_sales-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::UPDATE_COMPANIES))<li><x-sidebar.link-item :entity="'company_follow_ups_panel'" /></li>@endif
                        <li><x-sidebar.link-item :entity="'company_follow_ups_indicators'" /></li>
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyBillingModule())
                <li class="module-item" onclick="openDropdown('billing')">
                    <x-sidebar.module-item :icon="'fas fa-dollar-sign'" :module="\App\Core\Module::BILLING" />
                    <ul id="billing-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_BILLING_ENTRIES))<li><x-sidebar.link-item :entity="'billing_entries'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_COMPANY_CLOSING_GROUPS))<li><x-sidebar.link-item :entity="'company_closing_groups'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_STAND_ALONE_CONTRACTS_FOR_BILLING))<li><x-sidebar.link-item :entity="'stand_alone_contracts_for_billing'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_COMPANY_CLOSINGS))<li><x-sidebar.link-item :entity="'company_closings'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_BILLING_PANEL))<li><x-sidebar.link-item :entity="'billing_panel'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyFinanceModule())
                <li class="module-item" onclick="openDropdown('finance')">
                    <x-sidebar.module-item :icon="'fa fa-money'" :module="\App\Core\Module::FINANCE" />
                    <ul id="finance-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_RECEIVABLES))<li><x-sidebar.link-item :entity="'receivables'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewEngineeringModule())
                <li class="module-item" onclick="openDropdown('engineering')">
                    <x-sidebar.module-item :icon="'fa fa-hard-hat'" :module="\App\Core\Module::ENGINEERING" />
                    <ul id="engineering-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_SERVICE_ORDERS))<li><x-sidebar.link-item :entity="'service_orders_panel'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_SERVICE_ORDERS))<li><x-sidebar.link-item :entity="'service_orders_list'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyOperationModule())
                <li class="module-item" onclick="openDropdown('operation')">
                    <x-sidebar.module-item :icon="'fas fa-edit'" :module="\App\Core\Module::OPERATION" />
                    <ul id="operation-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_EXAMS))<li><x-sidebar.link-item :entity="'exams'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_COMPANY_CLOSING_GLOSSES))<li><x-sidebar.link-item :entity="'company_closing_glosses'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyKnowledgeBaseModule())
                <li class="module-item" onclick="openDropdown('knowledge_base')">
                    <x-sidebar.module-item :icon="'fas fa-database'" :module="\App\Core\Module::KNOWLEDGE_BASE" />
                    <ul id="knowledge_base-dropdown" class="dropdown-content">
                        @if (auth()->user()->can(\App\Models\Permission::GET_ARTICLES))<li><x-sidebar.link-item :entity="'articles'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_ARTICLE_CATEGORIES))<li><x-sidebar.link-item :entity="'article_categories'" /></li>@endif
                        @if (auth()->user()->can(\App\Models\Permission::GET_ARTICLE_TAGS))<li><x-sidebar.link-item :entity="'article_tags'" /></li>@endif
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyTicketsModule())
                <li class="module-item" onclick="openDropdown('tickets')">
                    <x-sidebar.module-item :icon="'fas fa-headset'" :module="\App\Core\Module::TICKETS" />
                    <ul id="tickets-dropdown" class="dropdown-content">
                        <li><x-sidebar.link-item :entity="'ticket_categories'" /></li>
                        <li><x-sidebar.link-item :entity="'tickets'" /></li>
                    </ul>
                </li>
            @endif
            @if (auth()->user()->viewsAnyReportModule())
                <a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('reports.index') }}">
                    <i class="fas fa-chart-line base-container-sidebar-lower-item-icon"></i>
                    <span class="base-container-sidebar-lower-item-link">{{ __('menu.reports') }}</span>
                </a>
            @endif
            @if (auth()->user()->viewsAnyImportModule())
                <a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('imports.index') }}">
                    <i class="fas fa-chart-line base-container-sidebar-lower-item-icon"></i>
                    <span class="base-container-sidebar-lower-item-link">{{ __('menu.imports') }}</span>
                </a>
            @endif
            @if (auth()->user()->hasRole(\App\Models\Role::ADMINISTRATOR))
                <li class="module-item" onclick="openDropdown('parameters')">
                    <x-sidebar.module-item :icon="'fas fa-cog'" :module="\App\Core\Module::PARAMETERS" />
                    <ul id="parameters-dropdown" class="dropdown-content">
                        <li><a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('management_parameters.edit', 1) }}"><span class="base-container-sidebar-lower-item-link">Cadastros</span></a></li>
                        <li><a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('contract_parameters.edit', 1) }}"><span class="base-container-sidebar-lower-item-link">Contratos</span></a></li>
                        <li><a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('crm_parameters.edit', 1) }}"><span class="base-container-sidebar-lower-item-link">CRM</span></a></li>
                        <li><a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('engineering_parameters.edit', 1) }}"><span class="base-container-sidebar-lower-item-link">Engenharia</span></a></li>
                        <li><x-sidebar.link-item :entity="'billing_parameters'" /></li>
                        <li><x-sidebar.link-item :entity="'finance_parameters'" /></li>
                        <li><x-sidebar.link-item :entity="'smtp_configurations'" /></li>
                    </ul>
                </li>
                <li class="module-item" onclick="openDropdown('auditing')">
                    <x-sidebar.module-item :icon="'fas fa-cog'" :module="\App\Core\Module::AUDITING" />
                    <ul id="auditing-dropdown" class="dropdown-content">
                        <li><x-sidebar.link-item :entity="'divergences'" /></li>
                        <li><x-sidebar.link-item :entity="'missing_company_closing_exams'" /></li>
                    </ul>
                </li>
                <li class="module-item" onclick="openDropdown('logs')">
                    <x-sidebar.module-item :icon="'fas fa-cog'" :module="\App\Core\Module::LOGS" />
                    <ul id="logs-dropdown" class="dropdown-content">
                        <li><x-sidebar.link-item :entity="'receivable_billing_emails'" /></li>
                        <li><x-sidebar.link-item :entity="'erp_flex_integration_logs'" /></li>
                        <li><x-sidebar.link-item :entity="'soc_integration_logs'" /></li>
                    </ul>
                </li>
                <li class="module-item" onclick="openDropdown('integrations')">
                    <x-sidebar.module-item :icon="'fas fa-sync'" :module="\App\Core\Module::INTEGRATIONS" />
                    <ul id="integrations-dropdown" class="dropdown-content">
                        <li><a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('erp_flex_integrations.edit', 1) }}"><span class="base-container-sidebar-lower-item-link">ERPFlex</span></a></li>
                        <li><x-sidebar.link-item :entity="'soc_integrations'" /></li>
                    </ul>
                </li>
            @endif
        </ul>
        <hr>
        {{-- @endif --}}
        <a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('users.change_password.get') }}">
            <i class="fas fa-lock base-container-sidebar-lower-item-icon"></i>
            <span class="base-container-sidebar-lower-item-link">{{ __('menu.change_password') }}</span>
        </a>
        <a class="d-flex align-items-center base-container-sidebar-lower-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
            <i class="fas fa-door-open base-container-sidebar-lower-item-icon"></i>
            <span class="base-container-sidebar-lower-item-link">{{ __('auth.logout') }}</span>
        </a>
        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
            @csrf
        </form>
    </div>
    <div class="px-3">
        <hr>
    </div>
    <div class="base-container-sidebar-footer">

    </div>
</div>

<script>
    function openDropdown(moduleName) {
        const modules = [];

        @if (auth()->user()->viewsAnyManagementModule())
            modules.push('management');
        @endif

        @if (auth()->user()->viewsAnyContractModule())
            modules.push('contracts');
        @endif

        @if (auth()->user()->viewsAnyAccreditationModule())
            modules.push('accreditation');
        @endif

        @if (auth()->user()->viewsAnyProvidingModule())
            modules.push('providing');
        @endif

        @if (auth()->user()->viewsAnyAfterSalesModule())
            modules.push('after_sales');
        @endif

        @if (auth()->user()->viewsAnyOperationModule())
            modules.push('operation');
        @endif

        @if (auth()->user()->viewsAnyBillingModule())
            modules.push('billing');
        @endif

        @if (auth()->user()->viewsAnyFinanceModule())
            modules.push('finance');
        @endif

        @if (auth()->user()->viewsAnyKnowledgeBaseModule())
            modules.push('knowledge_base');
        @endif

        @if (auth()->user()->viewsAnyTicketsModule())
            modules.push('tickets');
        @endif

        @if (auth()->user()->hasRole(\App\Models\Role::ADMINISTRATOR))
            modules.push('parameters');
            modules.push('auditing');
            modules.push('logs');
            modules.push('integrations');
        @endif

        modules
            .filter((item) => item !== moduleName)
            .forEach((module) => {
                document.getElementById(module + '-dropdown').classList.remove('show');
            })

        document.getElementById(moduleName + '-dropdown').classList.toggle('show');
    }
</script>
