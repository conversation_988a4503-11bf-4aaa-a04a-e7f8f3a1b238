@include('layouts.datatables_scripts')
<script src="{{ global_asset('js/core/components/data-tables/data-tables-helpers.js') }}"></script>
<script src="{{ global_asset('js/core/components/mask-money/mask-money-helpers.js') }}"></script>
<script src="{{ global_asset('js/core/components/jkanban/jkanban.min.js') }}"></script>
<script>
    let delayTimer;
    let kanban;

    function ucwords(str) {
        str = str.toLowerCase();

        let words = str.split(' ');
        str = '';

        for (var i = 0; i < words.length; i++) {
            let word = words[i];

            word = word.charAt(0).toUpperCase() + word.slice(1);

            if (i > 0) {
                str = str + ' ';
            }

            str = str + word;
        }

        return str;
    }

    function sendSearchValue(value) {
        const reworkFilter = document.getElementById('rework-filter').value;

        $.ajax("{{ route('service_orders.panel') }}", {
            data: {
                search: value,
                rework_filter: reworkFilter,
                '_token': "{{ csrf_token() }}"
            },
            method: 'post',
            success: function(response) {
                $('#service-orders-kanban').empty();
                kanban = initializeKanban(response);
            }
        });
    }

    function doSearch(value) {
        clearTimeout(delayTimer);
        delayTimer = setTimeout(function() {
            sendSearchValue(value);
        }, 1000);
    }

    function initializeScheduleDataTable(allDates = false) {
        initializeDataTable('schedule-list', "{{ route('schedules.get_available_schedules') }}", [{
                data: 'date',
                name: 'date'
            },
            {
                data: 'technician.name',
                name: 'technician.name'
            },
            {
                data: 'morning',
                name: 'morning'
            },
            {
                data: 'afternoon',
                name: 'afternoon'
            }
        ], {
            all_dates: allDates
        });
    }

    function initializeKanban(serviceOrders) {
        return new jKanban({
            element: "#service-orders-kanban",
            gutter: "10px",
            widthBoard: "350px",
            dragItems: false,
            dragBoards: false,
            boards: [
                {
                    id: "_liberated",
                    title: "Liberada",
                    item: getLiberatedBoardWithItems(serviceOrders)
                },
                {
                    id: "_scheduled",
                    title: "Agendada",
                    class: "warning",
                    item: getScheduledBoardWithItems(serviceOrders)
                },
                {
                    id: "_visited",
                    title: "Visitada",
                    class: "warning",
                    item: getVisitedBoardWithItems(serviceOrders)
                },
                {
                    id: "_checklist_received",
                    title: "Checklist recebido",
                    class: "warning",
                    item: getChecklistReceivedBoardWithItems(serviceOrders)
                },
                {
                    id: "_inserted",
                    title: "Digitada",
                    class: "warning",
                    item: getInsertedBoardWithItems(serviceOrders)
                },
                {
                    id: "_attended",
                    title: "Concluída",
                    class: "warning",
                    item: getAttendedBoardWithItems(serviceOrders)
                }
            ]
        });
    }

    function getCardTitle(serviceOrder) {
        let rework = '';

        if (serviceOrder.kanban_badge_rework !== '') {
            rework = `
                <h6 class="text-right">
                    <span class="badge badge-rework">
                    </span>
                </h6>
            `;
        }

        return `
            <h5>
                OS #${serviceOrder.id}
            </h5>
            <p>
                <span class='text-black-50 small'>
                    CLIENTE:
                </span><br>
                ${ucwords(serviceOrder.contract.company.name)}
            </p>
            <p>
                <span class='text-black-50 small'>
                    FILIAL/LOCAL:
                </span><br>
                ${ucwords(serviceOrder.branch?.branch_name ?? '')}
            </p>
            <p>
                <span class='text-black-50 small'>
                    PROCEDIMENTO:
                </span><br>
                ${ucwords(serviceOrder.procedure.name)}
            </p>
            <h6 class="text-right">
                <span class="badge badge-${serviceOrder.kanban_badge_status['badge']}">
                </span>
            </h6>
            ${rework}
            <h6 class="text-right">
                <span class="small">${getKanbanBadgeStatusText(serviceOrder.kanban_badge_status['day_count'])}</span>
            </h6>
        `;
    }

    function getKanbanBadgeStatusText(dayCount) {
        if (dayCount > 1) {
            return `Vencido há ${dayCount} dias`;
        } else {
            return dayCount === 1 ?
                `${dayCount} dia para mudar de status` :
                `${dayCount * -1} dias para mudar de status`;
        }
    }

    function getBaseCardInfo(serviceOrder) {
        try {
            return {
                service_order_id: `${serviceOrder.id}`,
                company_name: `${serviceOrder.contract.company.name}`,
                branch_name: `${serviceOrder.branch?.branch_name ?? ''}`,
                procedure_name: `${serviceOrder.procedure.name}`,
                base_date: `${serviceOrder.friendly_base_date}`,
                uses_database: `${serviceOrder.uses_database ? 'Sim' : 'Não'}`,
                zipcode: `${serviceOrder.zipcode}`,
                address: `${serviceOrder.address}`,
                number: `${serviceOrder.number}`,
                address_additional_info: `${serviceOrder.address_additional_info}`,
                district: `${serviceOrder.district}`,
                city: `${serviceOrder.city}`,
                state: `${serviceOrder.state}`,
                phone: `${serviceOrder.phone}`,
                in_charge_person: `${serviceOrder.in_charge_person}`,
                engineering_additional_info: `${serviceOrder.engineering_additional_info}`,
                email: `${serviceOrder.branch?.email}`,
                kanban_badge_status: `${serviceOrder.kanban_badge_status['badge']}`,
                kanban_badge_rework: `${serviceOrder.kanban_badge_rework}`,
                undo_last_transition_route: `${serviceOrder.undo_last_transition_route}`,
                pdf_route: `${serviceOrder.pdf_route}`,
                rework_details_route: `${serviceOrder.rework_details_route}`
            };
        } catch (error) {
            console.log(serviceOrder);
            console.log(error);
        }
    }

    function setBaseCardInfo(dataset, action) {
        $(`#mark-as-${action}-company-name`).text(dataset.company_name);
        $(`#mark-as-${action}-branch-name`).text(dataset.branch_name);
        $(`#mark-as-${action}-procedure-name`).text(dataset.procedure_name);
        $(`#mark-as-${action}-base-date`).text(dataset.base_date);
        $(`#mark-as-${action}-uses-database`).text(dataset.uses_database);
        $(`#mark-as-${action}-zipcode`).text(dataset.zipcode);
        $(`#mark-as-${action}-address`).text(dataset.address);
        $(`#mark-as-${action}-number`).text(dataset.number);
        $(`#mark-as-${action}-zipcode`).text(dataset.zipcode);
        $(`#mark-as-${action}-address-additional-info`).text(dataset.address_additional_info);
        $(`#mark-as-${action}-city`).text(dataset.city);
        $(`#mark-as-${action}-state`).text(dataset.state);
        $(`#mark-as-${action}-phone`).text(dataset.phone);
        $(`#mark-as-${action}-in-charge-person`).text(dataset.in_charge_person);
        $(`#mark-as-${action}-email`).text(dataset.email);
        $(`#mark-as-${action}-kanban-badge-status`).addClass('badge badge-' + dataset.kanban_badge_status);
        $(`#mark-as-${action}-kanban-badge-rework`).addClass('badge badge-' + dataset.kanban_badge_rework);
        $(`#mark-as-${action}-generate-pdf-btn`).attr('href', dataset.pdf_route);
        $(`#mark-as-${action}-rework-details-btn`).attr('href', dataset.rework_details_route);
        $('#undo-last-transition-modal').find('.modal-body').text(`Deseja desfazer a última transição da ordem ${dataset.service_order_id}?`);
        $('#undo-last-transition-form').attr('action', dataset.undo_last_transition_route);

        if ($('#mark-as-inserted-rework-details-btn').attr('href') !== '') {
            $(`#mark-as-${action}-rework-details-btn`).show();
        } else {
            $(`#mark-as-${action}-rework-details-btn`).hide();
        }
    }

    function getLiberatedBoardWithItems(serviceOrders) {
        const items = [];

        Array.from(serviceOrders).forEach(serviceOrder => {
            if (serviceOrder.status !== "{{ \App\Models\ServiceOrder::STATUS_LIBERATED }}") {
                return;
            }

            items.push({
                ...getBaseCardInfo(serviceOrder),
                id: `_liberated_${serviceOrder.id}`,
                title: getCardTitle(serviceOrder),
                schedule_route: `${serviceOrder.schedule_route}`,
                sale_price: `${serviceOrder.friendly_sale_price}`,
                purchase_price: `${serviceOrder.friendly_purchase_price}`,
                started_at: `${serviceOrder.friendly_started_at}`,
                schedule_additional_info: `${serviceOrder.schedule_additional_info}`,
                accumulated_liberation_additional_info: `${serviceOrder.accumulated_liberation_additional_info}`,
                click: function(el) {
                    setBaseCardInfo(el.dataset, 'scheduled');

                    $('#mark-as-scheduled-label').text('Agendar OS #' + el.dataset.service_order_id);
                    $(`#mark-as-scheduled-engineering-additional-info`).html(el.dataset.accumulated_liberation_additional_info);
                    $('#mark-as-scheduled-started-at').text(el.dataset.started_at);
                    $('#mark-as-scheduled-sale-price').val(el.dataset.sale_price);
                    $('#mark-as-scheduled-purchase-price').val(el.dataset.purchase_price);
                    $('#schedule_additional_info').val(null);

                    if (el.dataset.schedule_additional_info !== 'null') {
                        $('#schedule_additional_info').val(el.dataset.schedule_additional_info);
                    }

                    $('#mark-as-scheduled-form').prop('action', el.dataset.schedule_route);
                    $('#mark-as-scheduled-modal').modal('show');

                    maskDefault('mark-as-scheduled-sale-price');
                    maskDefault('mark-as-scheduled-purchase-price');

                    $('#mark-as-scheduled-purchase-price').on('change', function() {
                        const salePrice = $('#mark-as-scheduled-sale-price').maskMoney('unmasked')[0];
                        const purchasePrice = $('#mark-as-scheduled-purchase-price').maskMoney('unmasked')[0];
                        const balanceAmount = Math.round((salePrice * 0.6) * 100) / 100;
                        const lossRatio = Math.round((purchasePrice / salePrice) * 100) / 100;

                        if (purchasePrice === 0) {
                            $('#loss_ratio').html('INFORME O VL. DE COMPRA');
                            $('#loss_ratio').css('color', 'black');
                        } else if (purchasePrice < balanceAmount) {
                            $('#loss_ratio').html(Math.round((1 - (purchasePrice / balanceAmount)) * 100) + '% - LUCRO');
                            $('#loss_ratio').css('color', 'green');
                        } else if (purchasePrice > balanceAmount) {
                            $('#loss_ratio').html(Math.round((1 - (balanceAmount / purchasePrice)) * 100) + '% - PREJUÍZO');
                            $('#loss_ratio').css('color', 'red');
                        } else {
                            $('#loss_ratio').html(Math.round(lossRatio * 100) + '% - EQUILÍBRIO');
                            $('#loss_ratio').css('color', 'black');
                        }
                    });

                    $('#mark-as-scheduled-purchase-price').trigger('change');
                }
            });
        });

        return items;
    }

    function getScheduledBoardWithItems(serviceOrders) {
        const items = [];

        Array.from(serviceOrders).forEach(serviceOrder => {
            if (serviceOrder.status !== "{{ \App\Models\ServiceOrder::STATUS_SCHEDULED }}") {
                return;
            }

            items.push({
                ...getBaseCardInfo(serviceOrder),
                id: `_scheduled_${serviceOrder.id}`,
                title: getCardTitle(serviceOrder),
                visit_route: `${serviceOrder.visit_route}`,
                in_charge_technician: `${serviceOrder?.schedule_service_orders[0]?.schedule.technician.name}`,
                scheduled_by_user: `${serviceOrder.scheduling_user?.name}`,
                visit_additional_info: `${serviceOrder.visit_additional_info}`,
                accumulated_schedule_additional_info: `${serviceOrder.accumulated_schedule_additional_info}`,
                click: function(el) {
                    setBaseCardInfo(el.dataset, 'visited');

                    $('#mark-as-visited-label').text('Registrar visita da OS #' + el.dataset.service_order_id);
                    $('#mark-as-visited-technician').text(el.dataset.in_charge_technician);
                    $('#mark-as-visited-scheduled-by-user').text(el.dataset.scheduled_by_user);
                    $(`#mark-as-visited-engineering-additional-info`).html(el.dataset.accumulated_schedule_additional_info);
                    $('#visit_additional_info').val(null);

                    if (el.dataset.visit_additional_info !== 'null') {
                        $('#visit_additional_info').val(el.dataset.visit_additional_info);
                    }

                    $('#mark-as-visited-form').prop('action', el.dataset.visit_route);
                    $('#mark-as-visited-modal').modal('show');
                }
            });
        });

        return items;
    }

    function getVisitedBoardWithItems(serviceOrders) {
        const items = [];

        Array.from(serviceOrders).forEach(serviceOrder => {
            if (serviceOrder.status !== "{{ \App\Models\ServiceOrder::STATUS_VISITED }}") {
                return;
            }

            items.push({
                ...getBaseCardInfo(serviceOrder),
                id: `_visited_${serviceOrder.id}`,
                title: getCardTitle(serviceOrder),
                receive_checklist_route: `${serviceOrder.receive_checklist_route}`,
                in_charge_technician: `${serviceOrder?.schedule_service_orders[0]?.schedule.technician.name}`,
                scheduled_by_user: `${serviceOrder.scheduling_user?.name}`,
                visited_by_user: `${serviceOrder.visiting_user?.name}`,
                visited_at: `${serviceOrder.friendly_visited_at_from} - ${serviceOrder.friendly_visited_at_to}`,
                checklist_receiving_additional_info: `${serviceOrder.checklist_receiving_additional_info}`,
                accumulated_visit_additional_info: `${serviceOrder.accumulated_visit_additional_info}`,
                click: function(el) {
                    setBaseCardInfo(el.dataset, 'checklist-received');

                    $('#mark-as-checklist-received-label').text('Registrar recebimento de checklist da OS #' + el.dataset.service_order_id);
                    $('#mark-as-checklist-received-technician').text(el.dataset.in_charge_technician);
                    $('#mark-as-checklist-received-scheduled-by-user').text(el.dataset.scheduled_by_user);
                    $('#mark-as-checklist-received-visited-by-user').text(el.dataset.visited_by_user);
                    $('#mark-as-checklist-received-visited-at').text(el.dataset.visited_at);
                    $(`#mark-as-checklist-received-engineering-additional-info`).html(el.dataset.accumulated_visit_additional_info);
                    $('#checklist_receiving_additional_info').val(null);

                    if (el.dataset.checklist_receiving_additional_info !== 'null') {
                        $('#checklist_receiving_additional_info').val(el.dataset.checklist_receiving_additional_info);
                    }

                    $('#mark-as-checklist-received-form').prop('action', el.dataset.receive_checklist_route);
                    $('#mark-as-checklist-received-modal').modal('show');
                }
            });
        });

        return items;
    }

    function getChecklistReceivedBoardWithItems(serviceOrders) {
        const items = [];

        Array.from(serviceOrders).forEach(serviceOrder => {
            if (serviceOrder.status !== "{{ \App\Models\ServiceOrder::STATUS_CHECKLIST_RECEIVED }}") {
                return;
            }

            items.push({
                ...getBaseCardInfo(serviceOrder),
                id: `_checklist_received_${serviceOrder.id}`,
                title: getCardTitle(serviceOrder),
                insert_route: `${serviceOrder.insert_route}`,
                in_charge_technician: `${serviceOrder?.schedule_service_orders[0]?.schedule.technician.name}`,
                scheduled_by_user: `${serviceOrder.scheduling_user?.name}`,
                visited_by_user: `${serviceOrder.visiting_user?.name}`,
                visited_at: `${serviceOrder.friendly_visited_at_from} - ${serviceOrder.friendly_visited_at_to}`,
                checklist_received_by_user: `${serviceOrder.checklist_receiving_user?.name}`,
                checklist_received_at: `${serviceOrder.friendly_checklist_received_at}`,
                insert_additional_info: `${serviceOrder.insert_additional_info}`,
                accumulated_checklist_receiving_additional_info: `${serviceOrder.accumulated_checklist_receiving_additional_info}`,
                click: function(el) {
                    setBaseCardInfo(el.dataset, 'inserted');

                    $('#mark-as-inserted-label').text('Registrar digitação da OS #' + el.dataset.service_order_id);
                    $('#mark-as-inserted-technician').text(el.dataset.in_charge_technician);
                    $('#mark-as-inserted-scheduled-by-user').text(el.dataset.scheduled_by_user);
                    $('#mark-as-inserted-visited-by-user').text(el.dataset.visited_by_user);
                    $('#mark-as-inserted-visited-at').text(el.dataset.visited_at);
                    $('#mark-as-inserted-checklist-received-by-user').text(el.dataset.checklist_received_by_user);
                    $('#mark-as-inserted-checklist-received-at').text(el.dataset.checklist_received_at);
                    $(`#mark-as-inserted-engineering-additional-info`).html(el.dataset.accumulated_checklist_receiving_additional_info);
                    $('#insert_additional_info').val(null);

                    if (el.dataset.insert_additional_info !== 'null') {
                        $('#insert_additional_info').val(el.dataset.insert_additional_info);
                    }

                    $('#mark-as-inserted-form').prop('action', el.dataset.insert_route);
                    $('#mark-as-inserted-modal').modal('show');
                }
            });
        });

        return items;
    }

    function getInsertedBoardWithItems(serviceOrders) {
        const items = [];

        Array.from(serviceOrders).forEach(serviceOrder => {
            if (serviceOrder.status !== "{{ \App\Models\ServiceOrder::STATUS_INSERTED }}") {
                return;
            }

            items.push({
                ...getBaseCardInfo(serviceOrder),
                id: `_inserted_${serviceOrder.id}`,
                title: getCardTitle(serviceOrder),
                attend_route: `${serviceOrder.attend_route}`,
                in_charge_technician: `${serviceOrder?.schedule_service_orders[0]?.schedule.technician.name}`,
                scheduled_by_user: `${serviceOrder.scheduling_user?.name}`,
                visited_by_user: `${serviceOrder.visiting_user?.name}`,
                visited_at: `${serviceOrder.friendly_visited_at_from} - ${serviceOrder.friendly_visited_at_to}`,
                checklist_received_by_user: `${serviceOrder.checklist_receiving_user?.name}`,
                checklist_received_at: `${serviceOrder.friendly_checklist_received_at}`,
                inserted_by_user: `${serviceOrder.insertion_user?.name}`,
                inserted_at: `${serviceOrder.friendly_inserted_at}`,
                attendance_additional_info: `${serviceOrder.attendance_additional_info}`,
                accumulated_insert_additional_info: `${serviceOrder.accumulated_insert_additional_info}`,
                click: function(el) {
                    setBaseCardInfo(el.dataset, 'attended');

                    $('#mark-as-attended-label').text('Concluir OS #' + el.dataset.service_order_id);
                    $('#mark-as-attended-technician').text(el.dataset.in_charge_technician);
                    $('#mark-as-attended-scheduled-by-user').text(el.dataset.scheduled_by_user);
                    $('#mark-as-attended-visited-by-user').text(el.dataset.visited_by_user);
                    $('#mark-as-attended-visited-at').text(el.dataset.visited_at);
                    $('#mark-as-attended-checklist-received-by-user').text(el.dataset.checklist_received_by_user);
                    $('#mark-as-attended-checklist-received-at').text(el.dataset.checklist_received_at);
                    $('#mark-as-attended-inserted-by-user').text(el.dataset.inserted_by_user);
                    $('#mark-as-attended-inserted-at').text(el.dataset.inserted_at);
                    $(`#mark-as-attended-engineering-additional-info`).html(el.dataset.accumulated_insert_additional_info);
                    $('#attendance_additional_info').val(null);

                    if (el.dataset.attendance_additional_info !== 'null') {
                        $('#attendance_additional_info').val(el.dataset.attendance_additional_info);
                    }

                    $('#mark-as-attended-form').prop('action', el.dataset.attend_route);
                    $('#mark-as-attended-modal').modal('show');
                }
            });
        });

        return items;
    }

    function getAttendedBoardWithItems(serviceOrders) {
        const items = [];

        Array.from(serviceOrders).forEach(serviceOrder => {
            if (serviceOrder.status !== "{{ \App\Models\ServiceOrder::STATUS_ATTENDED }}") {
                return;
            }

            items.push({
                ...getBaseCardInfo(serviceOrder),
                id: `_attended_${serviceOrder.id}`,
                title: getCardTitle(serviceOrder),
                in_charge_technician: `${serviceOrder?.schedule_service_orders[0]?.schedule.technician.name}`,
                scheduled_by_user: `${serviceOrder.scheduling_user?.name}`,
                visited_by_user: `${serviceOrder.visiting_user?.name}`,
                visited_at: `${serviceOrder.friendly_visited_at_from} - ${serviceOrder.friendly_visited_at_to}`,
                checklist_received_by_user: `${serviceOrder.checklist_receiving_user?.name}`,
                checklist_received_at: `${serviceOrder.friendly_checklist_received_at}`,
                inserted_by_user: `${serviceOrder.insertion_user?.name}`,
                inserted_at: `${serviceOrder.friendly_inserted_at}`,
                attended_by_user: `${serviceOrder.attendance_user?.name}`,
                attended_at: `${serviceOrder.friendly_attended_at}`,
                accumulated_attendance_additional_info: `${serviceOrder.accumulated_attendance_additional_info}`,
                click: function(el) {
                    $('#attended-label').text('OS #' + el.dataset.service_order_id + ' - concluída');
                    $(`#attended-generate-pdf-btn`).attr('href', el.dataset.pdf_route);
                    $('#attended-scheduled-by-user').text(el.dataset.scheduled_by_user);
                    $('#attended-visited-by-user').text(el.dataset.visited_by_user);
                    $('#attended-visited-at').text(el.dataset.visited_at);
                    $('#attended-checklist-received-by-user').text(el.dataset.checklist_received_by_user);
                    $('#attended-checklist-received-at').text(el.dataset.checklist_received_at);
                    $('#attended-inserted-by-user').text(el.dataset.inserted_by_user);
                    $('#attended-inserted-at').text(el.dataset.inserted_at);
                    $('#attended-attended-by-user').text(el.dataset.attended_by_user);
                    $('#attended-attended-at').text(el.dataset.attended_at);
                    $('#attended-technician').text(el.dataset.in_charge_technician);
                    $('#attended-company-name').text(el.dataset.company_name);
                    $('#attended-branch-name').text(el.dataset.branch_name);
                    $('#attended-procedure-name').text(el.dataset.procedure_name);
                    $('#attended-base-date').text(el.dataset.base_date);
                    $('#attended-uses-database').text(el.dataset.uses_database);
                    $('#attended-zipcode').text(el.dataset.zipcode);
                    $('#attended-address').text(el.dataset.address);
                    $('#attended-number').text(el.dataset.number);
                    $('#attended-zipcode').text(el.dataset.zipcode);
                    $('#attended-address-additional-info').text(el.dataset.address_additional_info);
                    $('#attended-city').text(el.dataset.city);
                    $('#attended-state').text(el.dataset.state);
                    $('#attended-phone').text(el.dataset.phone);
                    $('#attended-in-charge-person').text(el.dataset.in_charge_person);
                    $(`#attended-engineering-additional-info`).html(el.dataset.accumulated_attendance_additional_info);
                    $('#attended-email').text(el.dataset.email);
                    $('#attended-kanban-badge-status').addClass('badge badge-' + el.dataset.kanban_badge_status);
                    $('#attended-form').prop('action', el.dataset.attend_route);
                    $('#attended-modal').modal('show');
                }
            });
        });

        return items;
    }

    $(document).ready(() => {
        initializeScheduleDataTable();

        $('#show-all-dates-checkbox').click(() => {
            $('#schedule-list').DataTable().destroy();
            initializeScheduleDataTable(JSON.stringify($('#show-all-dates-checkbox').is(':checked')));
        });

        kanban = initializeKanban({!! $serviceOrders !!});

        $(document).on('show.bs.modal', function(event) {
            let button = $(event.relatedTarget);

            if (button.data('target') === '#undo-last-transition-modal') {
                $('#mark-as-scheduled-modal').modal('hide');
                $('#mark-as-visited-modal').modal('hide');
                $('#mark-as-checklist-received-modal').modal('hide');
                $('#mark-as-inserted-modal').modal('hide');
                $('#mark-as-attended-modal').modal('hide');
            }
        });

        $('#mark_as_scheduled_additional_info_btn').click(() => {
            $('#mark_as_scheduled_apply_transition').val(0);
        });

        $('#mark_as_visited_additional_info_btn').click(() => {
            $('#mark_as_visited_apply_transition').val(0);
        });

        $('#mark_as_checklist_received_additional_info_btn').click(() => {
            $('#mark_as_checklist_received_apply_transition').val(0);
        });

        $('#mark_as_inserted_additional_info_btn').click(() => {
            $('#mark_as_inserted_apply_transition').val(0);
        });

        $('#mark_as_attended_additional_info_btn').click(() => {
            $('#mark_as_attended_apply_transition').val(0);
        });

        $('#undo-last-transition-form').submit(function(e) {
            $('#undo-last-transition-submit-btn').prop('disabled', true);
            $('#undo-last-transition-submit-btn').html('<i class="fas fa-hourglass mr-1"></i> Aguarde')
        });
    });
</script>
