@extends('layouts.app')

@section('content')
    <div>
        <form id="search-form" method="POST" class="once-only">
            @csrf
            <div class="my-3 ml-10">
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-6">
                        <label for="search" class="mb-1">Pesquisa</label>
                        <div class="d-flex rounded border">
                            <i class="d-flex fas fa-search align-items-center px-3"></i>
                            <input type="text" id="search" name="search" onkeyup="doSearch(this.value)" class="form-control d-flex border-0 shadow-none" placeholder="Digite a OS, o nome de um cliente, filial ou procedimento" />
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-3 col-lg-3">
                        <label for="rework-filter" class="mb-1">Retrabalho</label>
                        <select id="rework-filter" name="rework_filter" onchange="doSearch(document.getElementById('search').value)" class="form-control">
                            <option value="all">Todos os tipos</option>
                            <option value="rework_only">Apenas retrabalho</option>
                            <option value="normal">Apenas normal</option>
                        </select>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @include('base.components.error_alert')

    <div id="service-orders-kanban" style="width: auto;overflow-x: scroll;overflow-y: hidden;"></div>

    @include('app.engineering.service_orders.includes.undo_last_transition_modal')
    @include('app.engineering.service_orders.includes.mark_as_scheduled_modal')
    @include('app.engineering.service_orders.includes.mark_as_visited_modal')
    @include('app.engineering.service_orders.includes.mark_as_checklist_received_modal')
    @include('app.engineering.service_orders.includes.mark_as_inserted_modal')
    @include('app.engineering.service_orders.includes.mark_as_attended_modal')
    @include('app.engineering.service_orders.includes.attended_modal')
@endsection

@section('js-scripts')
    @include('app.engineering.service_orders.includes.scripts')
@endsection
