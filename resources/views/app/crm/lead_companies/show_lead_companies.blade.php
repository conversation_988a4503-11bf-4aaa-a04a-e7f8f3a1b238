@extends('layouts.app')

@section('content')
    <x-card :title="__('lead_companies.cards.show.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('lead_companies.index')">
        <x-form.edit-form :resourceRoute="$resourceRoute" :routeParameters="$leadCompany->id" :disabled="true">
            <x-tabs id="lead-company-edit">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="lead-company-edit-general" text="Geral" :active="true" />
                    <x-tabs.tab-header-item id="lead-company-edit-activity-information" text="Atividade" />
                    <x-tabs.tab-header-item id="lead-company-edit-contact" text="Contato" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="lead-company-edit-general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" :action="$action" field="tax_id_number" :value="$leadCompany->friendly_tax_id_number" required="true" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" :action="$action" field="name" :value="$leadCompany->name" required="true" />
                                <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" :action="$action" field="trading_name" :value="$leadCompany->trading_name" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.select md="3" lg="3" :resourceRoute="$resourceRoute" :action="$action" field="national_simple" required="true">
                                    <option value="0" @if (!$leadCompany->national_simple) selected @endif>Não</option>
                                    <option value="1" @if ($leadCompany->national_simple) selected @endif>Sim</option>
                                </x-input.select>
                                <div class="col-sm-12 col-md-3 col-lg-3">
                                    <div class="d-flex justify-content-between">
                                        <label for="state_registration_no" class="mb-1">Inscrição estadual</label>
                                        <div>
                                            <input type="checkbox" name="exempt_from_state_registration" id="exempt_from_state_registration" @if ($leadCompany->state_registration_no === 'ISENTO') checked @endif> Isento?
                                        </div>
                                    </div>
                                    <input type="text" id="state_registration_no" name="state_registration_no" value="{{ $leadCompany->state_registration_no }}" class="form-control" />
                                </div>
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" :action="$action" field="city_registration_no" :value="$leadCompany->city_registration_no" />
                            </x-form.row>
                        </x-form.section>
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="zipcode" :value="$leadCompany->zipcode" required="true" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" action="edit" field="address" :value="$leadCompany->address" required="true" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="number" :value="$leadCompany->number" required="true" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="additional_info" :value="$leadCompany->additional_info" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="district" :value="$leadCompany->district" required="true" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="city" :value="$leadCompany->city" required="true" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="state" :value="$leadCompany->state" required="true" maxLength="2" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="country" readonly="true" value="BR" required="true" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="lead-company-edit-activity-information">
                        <x-form.section>
                            <x-form.row>
                                <div class="col-sm-12 col-md-3 col-lg-3">
                                    <label class="mb-1" for="cnae">
                                        {{ __("$resourceRoute.cards.$action.body.fields.cnae") }}
                                        <span class="text-danger"><strong>*</strong></span>
                                    </label>
                                    <input type="text" class="form-control" id="cnae" name="cnae" autocomplete="off" value="{{ $leadCompany->cnae->code }}" required>
                                </div>
                                <div class="col-sm-12 col-md-3 col-lg-3">
                                    <label class="mb-1" for="cnae">
                                        {{ __("$resourceRoute.cards.$action.body.fields.nr04_risk_degree") }}
                                        <span class="text-danger"><strong>*</strong></span>
                                    </label>
                                    <input type="text" class="form-control" id="nr04_risk_degree" name="nr04_risk_degree" autocomplete="off" value="{{ $leadCompany->cnae->nr04_risk_degree }}" disabled>
                                </div>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <div class="col-12">
                                    <label class="mb-1" for="cnae">
                                        {{ __("$resourceRoute.cards.$action.body.fields.cnae_description") }}
                                    </label>
                                    <textarea name="cnae_description" id="cnae_description" rows="10" class="form-control" disabled>{{ $leadCompany->cnae->additional_info }}</textarea>
                                </div>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="lead-company-edit-contact">
                        <x-form.section :marginTop="true">
                            <livewire:lead-company-contact.tables.lead-company-contacts-table :leadCompanyId="$leadCompany->id" :manage="false" />
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.edit-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
    <script src="{{ global_asset('js/core/helpers.js') }}"></script>

    <script>
        $(document).ready(function() {
            const SPMaskBehavior = function(val) {
                return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
            };

            const spOptions = {
                onKeyPress: function(val, e, field, options) {
                    field.mask(SPMaskBehavior.apply({}, arguments), options);
                }
            };

            $('#zipcode').mask('00000-000');
            $('#cnae').mask('0000-0/00');

            $('.phone-input').mask(SPMaskBehavior, spOptions);
            $('#tax_id_number').mask('00.000.000/0000-00');

            $('#tax_id_number').blur(function() {
                $.ajax({
                    url: 'https://www.receitaws.com.br/v1/cnpj/' + this.value.match(/\d+/g).join(''),
                    crossDomain: true,
                    dataType: 'jsonp',
                    success: function(response) {
                        $('#name').val(response.nome);
                        $('#trading_name').val(response.fantasia);

                        $('#zipcode').val(response.cep.replace('.', ''));
                        $('#zipcode').trigger('blur');

                        $('#number').val(response.numero);
                        $('#additional_info').val(response.complemento);

                        $('#cnae').val(formatCnae(response.atividade_principal[0].code.replaceAll('.', '').replaceAll('-', '')));

                        document.getElementById('cnae').dispatchEvent(new Event('change'));
                    }
                });
            });

            $('#exempt_from_state_registration').click(() => {
                if ($('#exempt_from_state_registration').is(':checked')) {
                    $('#state_registration_no').val('ISENTO');
                    $('#state_registration_no').prop('readonly', true);
                } else {
                    $('#state_registration_no').val(null);
                    $('#state_registration_no').removeAttr('readonly');
                }
            });

            $('#zipcode').blur(function() {
                $.ajax({
                    url: `https://viacep.com.br/ws/${this.value}/json/`,
                    success: function(response) {
                        $('#additional_info').val(response.complemento);
                        $('#address').val(response.logradouro);
                        $('#district').val(response.bairro);
                        $('#city').val(response.localidade);
                        $('#state').val(response.uf);
                    }
                });
            });
        });
    </script>
@endsection
