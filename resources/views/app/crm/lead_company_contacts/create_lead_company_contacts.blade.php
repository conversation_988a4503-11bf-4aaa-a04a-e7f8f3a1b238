@extends('layouts.app')

@section('content')
    <x-card :title="__('lead_company_contacts.cards.create.header.title')" :cardHeaderTitle="true" :backButton="true">
        <x-form.create-form :$resourceRoute :routeParameters="$leadCompany->id">
            <x-form.row>
                <x-input.checkbox :$resourceRoute :$action field="main" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :required="true" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="email" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="phone" customClass="phone-input" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="job_title" />
            </x-form.row>
        </x-form.create-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>

    <script>
        $(document).ready(function() {
            const SPMaskBehavior = function (val) {
                return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
            };

            const spOptions = {
                onKeyPress: function (val, e, field, options) {
                    field.mask(SPMaskBehavior.apply({}, arguments), options);
                }
            };

            $('.phone-input').mask(SPMaskBehavior, spOptions);
        });
    </script>
@endsection
