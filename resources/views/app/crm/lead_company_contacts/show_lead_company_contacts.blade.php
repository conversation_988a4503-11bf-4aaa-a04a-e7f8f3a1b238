@extends('layouts.app')

@section('content')
    <x-card :title="__('lead_company_contacts.cards.show.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="url()->previous()">
        <x-form.edit-form :$resourceRoute :disabled="true">
            <x-form.row>
                <x-input.checkbox :$resourceRoute :$action field="main" :checked="$leadCompanyContact->main" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :required="true" :value="$leadCompanyContact->name" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="email" :value="$leadCompanyContact->email" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="phone" :value="$leadCompanyContact->phone" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="job_title" :value="$leadCompanyContact->job_title" />
            </x-form.row>
        </x-form.edit-form>
    </x-card>
@endsection
