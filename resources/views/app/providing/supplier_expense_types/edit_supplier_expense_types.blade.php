@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card-header d-flex align-items-center">
                    <strong class="card-header-title">{{ __("$resourceRoute.cards.edit.header.title") }}</strong>
                    <div class="ml-auto">
                        @include('base.components.buttons.back', ['route' => route('suppliers.edit', $supplierExpenseType->supplier_id)])
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-body">
                        <form method="post" class="form-group" action="{{ route('supplier_expense_types.update', [$supplierExpenseType->supplier_id, $supplierExpenseType->expense_type_id]) }}">
                            @csrf
                            @method('PUT')
                            @include('base.components.error_alert')

                            <fieldset>
                                <div class="row">
                                    <x-input.select md="6" lg="6" resourceRoute="supplier_expense_types" action="edit" field="expense_type_id" disabled="true">
                                        <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                    </x-input.select>
                                    <x-input.select md="6" lg="6" resourceRoute="supplier_expense_types" action="edit" field="expense_type_id" required="true">
                                        @foreach ($expenseTypes as $expenseType)
                                            <option value="{{ $expenseType->id }}" @if ($expenseType->id === $supplierExpenseType->expense_type_id) selected @endif >{{ $expenseType->name }}</option>
                                        @endforeach
                                    </x-input.select>
                                </div>
                            </fieldset>

                            <x-button.save />
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
