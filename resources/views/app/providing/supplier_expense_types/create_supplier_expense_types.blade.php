@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-12">
            @include('base.components.card.edit.header', ['route' => route('suppliers.edit', $supplier->id)])
            <div class="card shadow-sm">
                <div class="card-body">
                    <form class="form-group" method="post" action="{{ route('supplier_expense_types.store', $supplier->id) }}">
                        <fieldset>
                            @csrf
                            @include('base.components.error_alert')

                            <div class="row">
                                <x-input.select md="6" lg="6" resourceRoute="supplier_expense_types" action="create" field="expense_type_id" disabled="true">
                                    <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                </x-input.select>
                                <x-input.select md="6" lg="6" resourceRoute="supplier_expense_types" action="create" field="expense_type_id">
                                    @foreach ($expenseTypes as $expenseType)
                                        <option value="{{ $expenseType->id }}">{{ $expenseType->name }}</option>
                                    @endforeach
                                </x-input.select>
                            </div>
                        </fieldset>

                        <x-button.save />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
