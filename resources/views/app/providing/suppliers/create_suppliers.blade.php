@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <form class="form-group" method="post" action="{{ route('suppliers.store') }}">
                    <fieldset>
                        @csrf
                        @include('base.components.error_alert')

                        <div class="card-header d-flex align-items-center">
                            <strong class="card-header-title">{{ __("$resourceRoute.cards.create.header.title") }}</strong>
                            <div class="ml-auto">
                                <x-button.back :route="route('suppliers.index')" />
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Cliente</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.text md="6" lg="6" resourceRoute="suppliers" action="create" field="name" required />
                                    <x-input.text md="6" lg="6" resourceRoute="suppliers" action="create" field="trading_name" required />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="email" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="tax_id_number" maxLength="14" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="state_registration_no" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="city_registration_no" />
                                </div>
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Endereço</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="zipcode" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text md="6" lg="6" resourceRoute="suppliers" action="create" field="address" readonly="true" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="number" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="additional_info" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="district" readonly="true" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="city" readonly="true" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="state" readonly="true" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="country" readonly="true" value="BR" />
                                </div>
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Contato</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.text md="4" lg="4" resourceRoute="suppliers" action="create" field="phone_1" customClass="phone-input" />
                                    <x-input.text md="4" lg="4" resourceRoute="suppliers" action="create" field="phone_2" customClass="phone-input" />
                                    <x-input.text md="4" lg="4" resourceRoute="suppliers" action="create" field="phone_3" customClass="phone-input" />
                                </div>
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Outras informações</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="system" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="suframa" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="gln" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="import_document" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.select md="3" lg="3" resourceRoute="suppliers" action="create" field="qualified">
                                        <option value="1">Sim</option>
                                        <option value="0">Não</option>
                                    </x-input.select>
                                    <x-input.date md="3" lg="3" resourceRoute="suppliers" action="create" field="qualification_expires_at" />
                                    <x-input.select md="3" lg="3" resourceRoute="suppliers" action="create" field="national_simple_tax">
                                        <option value="1">Sim</option>
                                        <option value="0">Não</option>
                                    </x-input.select>
                                </div>
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Informações bancárias</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.select md="4" lg="4" resourceRoute="suppliers" action="create" field="preferred_earning_method">
                                        <option value="" disabled selected>Escolha um...</option>
                                        <option value="cash">Dinheiro</option>
                                        <option value="transfer">Transferência</option>
                                        <option value="pix">PIX</option>
                                    </x-input.select>
                                </div>
                                <div class="row mt-4">
                                    <x-input.select md="6" lg="6" resourceRoute="suppliers" action="create" field="bank_id"></x-input.select>
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="bank_code" disabled="true" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="branch_number" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="branch_digit" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="account_number" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="account_digit" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text resourceRoute="suppliers" action="create" field="pix_1" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text resourceRoute="suppliers" action="create" field="pix_2" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text resourceRoute="suppliers" action="create" field="pix_3" />
                                </div>
                            </div>
                        </div>

                        <x-button.save />
                    </fieldset>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('js-scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>
<script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
<script>
    $(document).ready(function() {
        const SPMaskBehavior = function (val) {
            return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
        };

        const spOptions = {
            onKeyPress: function(val, e, field, options) {
                field.mask(SPMaskBehavior.apply({}, arguments), options);
            }
        };

        const taxIdNumberOptions = {
            onKeyPress: function (cpf, ev, el, op) {
                var masks = ['000.000.000-000', '00.000.000/0000-00'];
                $('.tax-id-number-input').mask((cpf.length > 14) ? masks[1] : masks[0], op);
            }
        };

        $('#zipcode').mask('00000-000');
        $('#number').mask('99999');

        $('.phone-input').mask(SPMaskBehavior, spOptions);
        $('.tax-id-number-input').mask('000.000.000-00', spOptions);

        $('#tax_id_number').blur(function() {
            $.ajax({
                url: `https://www.receitaws.com.br/v1/cnpj/${this.value}`,
                headers: {
                    accept: 'application/json',
                    "Access-Control-Allow-Origin": '*'
                },
                success: function(response) {
                    $('#name').attr('readonly', response.nome !== '');
                    $('#trading_name').attr('readonly', response.fantasia !== '');

                    $('#name').val(response.nome);
                    $('#trading_name').val(response.fantasia);
                }
            });
        });

        $('#zipcode').blur(function() {
            $.ajax({
                url: `https://viacep.com.br/ws/${this.value}/json/`,
                success: function(response) {
                    if (response.logradouro !== '') {
                        $('#address').prop('readonly', true);
                        $('#number').focus();
                    } else {
                        $('#address').prop('readonly', false);
                        $('#address').focus();
                    }

                    $('#additional_info').attr('readonly', response.complemento !== '');
                    $('#district').attr('readonly', response.bairro !== '');
                    $('#city').attr('readonly', response.localidade !== '');
                    $('#state').attr('readonly', response.uf !== '');
                    $('#additional_info').val(response.complemento);
                    $('#address').val(response.logradouro);
                    $('#district').val(response.bairro);
                    $('#city').val(response.localidade);
                    $('#state').val(response.uf);
                }
            });
        });

        initializeSelect2(
            'bank_id',
            "{{ route('banks.get_by_code_or_name') }}",
            'Digite o código ou o nome de um banco',
            function (item) {
                return {
                    id: item.id,
                    text: item.name,
                    code: item.code
                };
            }
        );

        $('#bank_id').on('select2:select', function (e) {
            $('#bank_code').val(e.params.data.code);
        });
    });
</script>
@endsection
