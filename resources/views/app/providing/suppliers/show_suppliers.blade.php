@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card-header align-items-center">
                    <div class="row d-flex align-items-center">
                        <strong class="card-header-title">{{ __("$resourceRoute.cards.show.header.title") }}</strong>
                        <div class="ml-auto">
                            @include('base.components.buttons.back', ['route' => $route ?? route("$resourceRoute.index")])
                        </div>
                    </div>
                </div>

                <form class="form-group" method="post" action="{{ route('suppliers.update', $supplier->id) }}">
                    <fieldset disabled>
                        @csrf
                        @method('PUT')
                        @include('base.components.error_alert')

                        <div class="card-header d-flex align-items-center">
                            <strong>Dados gerais</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.text md="6" lg="6" resourceRoute="suppliers" action="show" field="name" value="{{ $supplier->name }}" required />
                                    <x-input.text md="6" lg="6" resourceRoute="suppliers" action="show" field="trading_name" value="{{ $supplier->trading_name }}" required />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="email" value="{{ $supplier->email }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="tax_id_number" value="{{ $supplier->tax_id_number }}" maxLength="14" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="state_registration_no" value="{{ $supplier->state_registration_no }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="city_registration_no" value="{{ $supplier->city_registration_no }}" />
                                </div>
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Endereço</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="zipcode" value="{{ $supplier->zipcode }}" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text md="6" lg="6" resourceRoute="suppliers" action="show" field="address" value="{{ $supplier->address }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="number" value="{{ $supplier->number }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="additional_info" value="{{ $supplier->additional_info }}" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="district" value="{{ $supplier->district }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="city" value="{{ $supplier->city }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="state" value="{{ $supplier->state }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="country" value="{{ $supplier->country }}" />
                                </div>
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Contato</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.text md="4" lg="4" resourceRoute="suppliers" action="show" field="phone_1" value="{{ $supplier->phone_1 }}" />
                                    <x-input.text md="4" lg="4" resourceRoute="suppliers" action="show" field="phone_2" value="{{ $supplier->phone_2 }}" />
                                    <x-input.text md="4" lg="4" resourceRoute="suppliers" action="show" field="phone_3" value="{{ $supplier->phone_3 }}" />
                                </div>
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Outras informações</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="system" value="{{ $supplier->system }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="suframa" value="{{ $supplier->suframa }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="gln" value="{{ $supplier->gln }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="show" field="import_document" value="{{ $supplier->import_document }}" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.select md="3" lg="3" resourceRoute="suppliers" action="show" field="qualified">
                                        <option value="1" @if ($supplier->qualified) selected @endif>Sim</option>
                                        <option value="0" @if (!$supplier->qualified) selected @endif>Não</option>
                                    </x-input.select>
                                    <x-input.date md="3" lg="3" resourceRoute="suppliers" action="show" field="qualification_expires_at" value="{{ $supplier->qualification_expires_at }}" />
                                    <x-input.select md="3" lg="3" resourceRoute="suppliers" action="show" field="national_simple_tax">
                                        <option value="1" @if ($supplier->national_simple_tax) selected @endif>Sim</option>
                                        <option value="0" @if (!$supplier->national_simple_tax) selected @endif>Não</option>
                                    </x-input.select>
                                </div>
                            </div>
                        </div>

                        <div class="card-header d-flex align-items-center mt-4">
                            <strong>Informações bancárias</strong>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <x-input.select md="4" lg="4" resourceRoute="suppliers" action="show" field="preferred_earning_method">
                                        <option value="" disabled selected>Escolha um...</option>
                                        <option value="cash" @if ($supplier->preferred_earning_method === 'cash') selected @endif>Dinheiro</option>
                                        <option value="transfer" @if ($supplier->preferred_earning_method === 'transfer') selected @endif>Transferência</option>
                                        <option value="pix" @if ($supplier->preferred_earning_method === 'pix') selected @endif>PIX</option>
                                    </x-input.select>
                                </div>
                                <div class="row mt-4">
                                    <x-input.select md="6" lg="6" resourceRoute="suppliers" action="create" field="bank_id">
                                        <option value="{{ $supplier->bank_id }}">{{ $supplier->bank?->name }}</option>
                                    </x-input.select>
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="bank_code" disabled="true" value="{{ $supplier->bank?->code }}" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="branch_number" value="{{ $supplier->branch_number }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="branch_digit" value="{{ $supplier->branch_digit }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="account_number" value="{{ $supplier->account_number }}" />
                                    <x-input.text md="3" lg="3" resourceRoute="suppliers" action="create" field="account_digit" value="{{ $supplier->account_digit }}" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text resourceRoute="suppliers" action="create" field="pix_1" value="{{ $supplier->pix_1 }}" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text resourceRoute="suppliers" action="create" field="pix_2" value="{{ $supplier->pix_2 }}" />
                                </div>
                                <div class="row mt-4">
                                    <x-input.text resourceRoute="suppliers" action="create" field="pix_3" value="{{ $supplier->pix_3 }}" />
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <hr>
    </div>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card-header align-items-center">
                    <div class="row d-flex align-items-center">
                        <strong class="card-header-title">{{ __('Tipos de despesas') }}</strong>
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-body">
                        <table id="list" class="table row-bordered table-striped" cellspacing="0" width="100%">
                            <thead>
                                <tr>
                                    <th>{{ __('Nome') }}</th>
                                    <th>{{ __('Ações') }}</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="send-to-erp-flex-modal" tabindex="-1" role="dialog" aria-labelledby="send-to-erp-flex-label" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form id="send-to-erp-flex-form" method="POST" action="{{ route('suppliers.send_to_erp_flex', $supplier->id) }}">
                    @csrf
                    <input type="hidden" name="emit_invoice" value="0" />
                    <div class="modal-header">
                        <h5 class="modal-title" id="exam-gloss-label">Enviar para ERPFlex</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        Deseja enviar o fornecedor para o ERPFlex?
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">{{ __('buttons.no') }}</button>
                        <button type="submit" class="btn btn-primary">{{ __('buttons.yes') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @include('base.components.modals.delete_modal', ['resourceRoute' => 'supplier_expense_types'])
    @include('layouts.datatables_scripts')
@endsection

@section('js-scripts')
<script src="{{ global_asset('js/core/components/data-tables/data-tables-helpers.js') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>
<script>
    $(document).ready(() => {
        initializeDataTable('list', "{{ route('supplier_expense_types.get_for_datatable', $supplier->id) }}", [
            {data: 'expense_type.name', name: 'expenseType.name'},
            {data: 'actions', name: 'actions'},
        ]);

        const SPMaskBehavior = function (val) {
            return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
        };

        const spOptions = {
            onKeyPress: function(val, e, field, options) {
                field.mask(SPMaskBehavior.apply({}, arguments), options);
            }
        };

        const taxIdNumberOptions = {
            onKeyPress: function (cpf, ev, el, op) {
                var masks = ['000.000.000-000', '00.000.000/0000-00'];
                $('.tax-id-number-input').mask((cpf.length > 14) ? masks[1] : masks[0], op);
            }
        };

        $('#zipcode').mask('00000-000');
        $('#number').mask('99999');

        $('.phone-input').mask(SPMaskBehavior, spOptions);
        $('.tax-id-number-input').mask('000.000.000-00', spOptions);

        $('#tax_id_number').blur(function() {
            $.ajax({
                url: `https://www.receitaws.com.br/v1/cnpj/${this.value}`,
                headers: {
                    accept: 'application/json',
                    "Access-Control-Allow-Origin": '*'
                },
                success: function(response) {
                    $('#name').attr('readonly', response.nome !== '');
                    $('#trading_name').attr('readonly', response.fantasia !== '');

                    $('#name').val(response.nome);
                    $('#trading_name').val(response.fantasia);
                }
            });
        });

        $('#zipcode').blur(function() {
            $.ajax({
                url: `https://viacep.com.br/ws/${this.value}/json/`,
                success: function(response) {
                    if (response.logradouro !== '') {
                        $('#address').prop('readonly', true);
                        $('#number').focus();
                    } else {
                        $('#address').prop('readonly', false);
                        $('#address').focus();
                    }

                    $('#additional_info').attr('readonly', response.complemento !== '');
                    $('#district').attr('readonly', response.bairro !== '');
                    $('#city').attr('readonly', response.localidade !== '');
                    $('#state').attr('readonly', response.uf !== '');
                    $('#additional_info').val(response.complemento);
                    $('#address').val(response.logradouro);
                    $('#district').val(response.bairro);
                    $('#city').val(response.localidade);
                    $('#state').val(response.uf);
                }
            });
        });

        $(document).on('show.bs.modal', function (event) {
            let button = $(event.relatedTarget);

            if (button.data('target') === '#delete-modal') {
                let name = button.data("supplier-expense-type-name");
                let link = button.data("supplier-expense-type-destroy-link");

                let modal = $(this);
                modal.find('.modal-body').text('Deseja excluir o vínculo para o tipo de despesa ' + name + '?');
                modal.find('#delete-form').attr('action', link);
            }
        });
    });
</script>
@endsection
