@extends('layouts.app')

@section('content')
<div class="px-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card-header d-flex align-items-center">
                <strong class="card-header-title">{{ __("$resourceRoute.cards.index.header.title") }}</strong>
                <div class="ml-auto">
                    @role(\App\Models\Role::ADMINISTRATOR)
                    <button class="btn btn-outline-secondary shadow-sm mr-2" data-toggle="modal" data-target="#sync-modal">
                        <i class="fa fa-sync mr-1"></i> Sincronizar
                    </button>
                    <button class="btn btn-outline-secondary shadow-sm mr-2" data-toggle="modal" data-target="#import-modal">
                        <i class="fa fa-upload mr-1"></i> Importar
                    </button>
                    @endrole
                    @include('base.components.buttons.create', ['route' => route("$resourceRoute.create")])
                </div>
            </div>

            <x-index-card.filter-box>
                <div class="row">
                    <div class="col-sm-12 col-md-3 col-lg-3">
                        <label for="active" class="mb-1">Ativo</label>
                        <select name="active" id="active" class="form-control">
                            <option value="">Todos</option>
                            <option value="1">Somente ativos</option>
                            <option value="0">Somente inativos</option>
                        </select>
                    </div>
                    <div class="col-sm-12 col-md-3 col-lg-3">
                        <label for="integrated_with_erp_flex" class="mb-1">Integração com ERPFlex</label>
                        <select name="integrated_with_erp_flex" id="integrated_with_erp_flex" class="form-control">
                            <option value="">Todos</option>
                            <option value="1">Somente integrados</option>
                            <option value="0">Somente não integrados</option>
                        </select>
                    </div>
                </div>
            </x-index-card.filter-box>

            <x-datatable.table :withMargins="false">
                <th>Razão social</th>
                <th>Nome fantasia</th>
                <th>CNPJ</th>
                <th>Email</th>
                <th>Ativo</th>
                <th>Integrado com ERPFlex</th>
                <th>Ações</th>
            </x-datatable.table>
        </div>
    </div>
</div>

<div class="modal fade" id="sync-modal" tabindex="-1" role="dialog" aria-labelledby="sync-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="sync-form" method="POST" action="{{ route('suppliers.get_from_erp_flex') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="sync-label">{{ __("$resourceRoute.modals.index.sync.title") }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Deseja sincronizar os fornecedores com o ERPFlex? O processo rodará em segundo plano e você receberá uma notificação ao término do processo.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">{{ __('buttons.no') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('buttons.yes') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<x-modal.delete :resourceRoute="$resourceRoute" />
<x-modal.import :resourceRoute="$resourceRoute" />
<x-modal.change-activity :resourceRoute="$resourceRoute" />
@endsection

@section('js-scripts')
<script>
    function initializeDataTable() {
        return $('#list').DataTable({
            language: {
                url: "//cdn.datatables.net/plug-ins/1.10.20/i18n/Portuguese-Brasil.json"
            },
            processing: true,
            serverSide: true,
            responsive: true,
            ajax: {
                url: "{{ route('suppliers.get_for_datatable') }}",
                data: function (d) {
                    d.active = $('#active').val(),
                    d.integrated_with_erp_flex = $('#integrated_with_erp_flex').val(),
                    d.search = $('input[type="search"]').val()
                }
            },
            columns: [
                {data: 'name', name: 'name'},
                {data: 'trading_name', name: 'trading_name'},
                {data: 'tax_id_number', name: 'tax_id_number', width: '140px'},
                {data: 'email', name: 'email'},
                {data: 'active', name: 'active'},
                {data: 'integrated_with_erp_flex', name: 'integrated_with_erp_flex'},
                {data: 'actions', name: 'actions', width: '160px'},
            ]
        });
    }

    function loadModalEvents() {
        $(document).on('show.bs.modal', function(event) {
            let button = $(event.relatedTarget);

            if (button.data('target') === '#delete-modal') {
                let name = button.data("supplier-name");
                let link = button.data("supplier-destroy-link");

                let modal = $(this);
                modal.find('.modal-body').text('Deseja excluir o fornecedor ' + name + '?');
                modal.find('#delete-form').attr('action', link);
            } else if (button.data('target') === '#change-activity-modal') {
                let name = button.data("supplier-name");
                let link = button.data("supplier-change-activity-link");
                let action = button.data("supplier-action");

                let modal = $(this);
                modal.find('.modal-body').text('Deseja ' + action + ' o fornecedor ' + name + '?');
                modal.find('#change-activity-form').attr('action', link);
            }
        });
    }

    $(document).ready(function() {
        const table = initializeDataTable();

        $('#active').change(() => table.draw());
        $('#integrated_with_erp_flex').change(() => table.draw());

        loadModalEvents();
    });
</script>
@endsection
