@extends('layouts.app')

@section('content')
    <div class="px-4">
        <div class="row justify-content-center">
            <div class="col-12">
                @include('base.components.card.index.header', compact('resourceRoute', 'module', 'createsResource'))

                <x-index-card.filter-box>
                    <div class="row">
                        <div class="col-sm-12 col-md-3 col-lg-3">
                            <label for="entered_at_from" class="mb-1">Data de lançamento de</label>
                            <input type="date" id="entered_at_from" name="entered_at_from" class="form-control" value="{{ now()->subMonth()->format('Y-m-d') }}" />
                        </div>
                        <div class="col-sm-12 col-md-3 col-lg-3">
                            <label for="entered_at_to" class="mb-1">Data de lançamento até</label>
                            <input type="date" id="entered_at_to" name="entered_at_to" class="form-control" value="{{ now()->format('Y-m-d') }}" />
                        </div>
                        <div class="col-sm-12 col-md-3 col-lg-3">
                            <label for="status" class="mb-1">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">Todos</option>
                                <option value="{{ \App\Models\SupplierExpense::STATUS_PENDING }}">Pendente</option>
                                <option value="{{ \App\Models\SupplierExpense::STATUS_APPROVED }}">Aprovado</option>
                                <option value="{{ \App\Models\SupplierExpense::STATUS_REJECTED }}">Rejeitado</option>
                            </select>
                        </div>
                    </div>
                </x-index-card.filter-box>

                <div class="card shadow-sm">
                    <div class="card-body">
                        <table id="list" class="row-bordered table-striped table" cellspacing="0" width="100%">
                            <thead>
                                <tr>
                                    <th>{{ __('Doc.') }}</th>
                                    <th>{{ __('NF') }}</th>
                                    <th>{{ __('Data de lançamento') }}</th>
                                    <th>{{ __('Fornecedor') }}</th>
                                    <th>{{ __('Valor bruto') }}</th>
                                    <th>{{ __('Valor líquido') }}</th>
                                    @role(\App\Models\Role::ADMINISTRATOR)
                                        <th>{{ __('Lançado por') }}</th>
                                    @endrole
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Ações') }}</th>
                                    <th>{{ __('Anexos') }}</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <x-modal.delete :resourceRoute="$resourceRoute" />

    <x-modal.default-action id="approve" route="" title="Aprovar despesa" />

    <x-modal.default-action id="reject" route="" title="Rejeitar despesa">
        <div class="row">
            <div class="col-12">
                <label for="rejection_reason" class="mb-1">Informe o motivo de rejeição da despesa</label>
                <textarea name="rejection_reason" id="rejection_reason" rows="10" maxlength="255" class="form-control"></textarea>
            </div>
        </div>
    </x-modal.default-action>

    <x-modal.info id="attachments" title="Anexos">
        <div id="item-group"></div>
    </x-modal.info>
@endsection

@section('js-scripts')
    @include('layouts.datatables_scripts')
    <script>
        function addAttachment(filename, downloadRoute, newCount = null) {
            let currentDatasetId = 0;

            if ($('.group-item').length > 0) {
                const groupItems = document.getElementsByClassName('group-item');
                currentDatasetId = groupItems[groupItems.length - 1].dataset.id;
            }

            if (!newCount) {
                newCount = parseInt(currentDatasetId) + 1;
            }

            const rowDiv = document.createElement('div');
            rowDiv.id = 'group_item_${newCount}';
            rowDiv.className = 'row mb-3';
            rowDiv.dataset.id = newCount;

            const col12Div = document.createElement('div');
            col12Div.className = 'col-12';
            col12Div.dataset.id = newCount;

            const cardDiv = document.createElement('div');
            cardDiv.className = 'card shadow-sm';
            cardDiv.dataset.id = newCount;

            const cardBodyDiv = document.createElement('div');
            cardBodyDiv.className = 'card-body d-flex justify-content-between align-items-center';

            const span = document.createElement('span');
            span.innerHTML = filename;

            const downloadButton = document.createElement('a');
            downloadButton.href = downloadRoute;
            downloadButton.className = 'btn btn-sm btn-outline-primary';

            const i = document.createElement('i');
            i.className = 'fa fa-download';

            downloadButton.appendChild(i);
            cardBodyDiv.appendChild(span);
            cardBodyDiv.appendChild(downloadButton);
            cardDiv.appendChild(cardBodyDiv);
            col12Div.appendChild(cardDiv);
            rowDiv.appendChild(col12Div);

            $('#item-group').append(rowDiv);
        }

        function loadModalEvents() {
            $(document).on('show.bs.modal', function(event) {
                let button = $(event.relatedTarget);

                if (button.data('target') === '#delete-modal') {
                    let name = button.data("supplier-expense-id");
                    let link = button.data("supplier-expense-destroy-link");

                    let modal = $(this);
                    modal.find('.modal-body').text('Deseja excluir a despesa #' + name + '?');
                    modal.find('#delete-form').attr('action', link);
                } else if (button.data('target') === '#approve-modal') {
                    let name = button.data("supplier-expense-id");
                    let link = button.data("supplier-expense-approve-link");

                    let modal = $(this);
                    modal.find('.modal-body').text('Deseja aprovar a despesa #' + name + '?');
                    modal.find('#approve-form').attr('action', link);
                } else if (button.data('target') === '#reject-modal') {
                    let name = button.data("supplier-expense-id");
                    let link = button.data("supplier-expense-reject-link");

                    let modal = $(this);
                    modal.find('#reject-form').attr('action', link);
                } else if (button.data('target') === '#attachments-modal') {
                    const id = button.data('supplier-expense-id');
                    $.ajax('supplier-expenses/' + id + '/files', {
                        success: function(response) {
                            const files = JSON.parse(response);

                            $('#item-group').empty();

                            files.forEach(file => addAttachment(file.filename, file.download_link, file.id))
                        }
                    });
                }
            });
        }

        function initializeDataTable() {
            return $('#list').DataTable({
                language: {
                    url: "//cdn.datatables.net/plug-ins/1.10.20/i18n/Portuguese-Brasil.json"
                },
                processing: true,
                serverSide: true,
                responsive: true,
                ajax: {
                    url: "{{ route('supplier_expenses.index') }}",
                    data: function(d) {
                        d.entered_at_from = $('#entered_at_from').val(),
                            d.entered_at_to = $('#entered_at_to').val(),
                            d.status = $('#status').val(),
                            d.search = $('input[type="search"]').val()
                    }
                },
                columns: [{
                        data: 'id',
                        name: 'id'
                    },
                    {
                        data: 'document_number',
                        name: 'document_number'
                    },
                    {
                        data: 'entered_at',
                        name: 'entered_at'
                    },
                    {
                        data: 'supplier.name',
                        name: 'supplier.name'
                    },
                    {
                        data: 'gross_amount',
                        name: 'gross_amount',
                        render: (data) => 'R$ ' + data.toString().replace('.', ',')
                    },
                    {
                        data: 'net_amount',
                        name: 'net_amount',
                        render: (data) => 'R$ ' + data.toString().replace('.', ',')
                    },
                    @role(\App\Models\Role::ADMINISTRATOR)
                        {
                            data: 'operator.name',
                            name: 'operator.name'
                        },
                    @endrole {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'actions',
                        name: 'actions'
                    },
                    {
                        data: 'attachments',
                        name: 'attachments'
                    },
                ]
            });
        }

        $(document).ready(function() {
            const table = initializeDataTable();

            $('#status').change(() => table.draw());
            $('#entered_at_from').change(() => table.draw());
            $('#entered_at_to').change(() => table.draw());

            loadModalEvents();
        });
    </script>
@endsection
