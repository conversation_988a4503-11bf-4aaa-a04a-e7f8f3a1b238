@extends('layouts.app')

@section('content')
    <x-card :title="__('supplier_qualification_criteria.cards.edit.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('supplier_qualification_criteria.index')">
        <x-form.edit-form :$resourceRoute :routeParameters="$supplierQualificationCriterion">
            <x-tabs id="supplier-qualification-criterion-edit">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="supplier-qualification-criterion-edit-general" text="Geral" :active="true" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="supplier-qualification-criterion-edit-general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :value="$supplierQualificationCriterion->name" :required="true" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="sequence" :value="$supplierQualificationCriterion->sequence" :required="true" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="min_grade" :value="$supplierQualificationCriterion->min_grade" :required="true" step="0.01" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="max_grade" :value="$supplierQualificationCriterion->max_grade" :required="true" step="0.01" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="step" :value="$supplierQualificationCriterion->step" :required="true" step="0.01" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="weight" :value="$supplierQualificationCriterion->weight" :required="true" step="0.01" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.edit-form>
    </x-card>
@endsection
