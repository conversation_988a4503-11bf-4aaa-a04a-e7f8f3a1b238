@extends('layouts.app')

@section('content')
    <x-card :title="__('supplier_qualification_criteria.cards.show.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('supplier_qualification_criteria.index')">
        <x-tabs id="supplier-qualification-criterion-show">
            <x-slot name="tabHeaders">
                <x-tabs.tab-header-item id="supplier-qualification-criterion-show-general" text="Geral" :active="true" />
            </x-slot>
            <x-slot name="tabContent">
                <x-tabs.tab-content-item id="supplier-qualification-criterion-show-general" :active="true">
                    <x-form.section>
                        <x-form.row>
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :value="$supplierQualificationCriterion->name" :disabled="true" />
                            <x-input.text md="3" lg="3" :$resourceRoute :$action field="sequence" :value="$supplierQualificationCriterion->sequence" :disabled="true" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.text md="3" lg="3" :$resourceRoute :$action field="min_grade" :value="number_format($supplierQualificationCriterion->min_grade, 2, ',', '.')" :disabled="true" />
                            <x-input.text md="3" lg="3" :$resourceRoute :$action field="max_grade" :value="number_format($supplierQualificationCriterion->max_grade, 2, ',', '.')" :disabled="true" />
                            <x-input.text md="3" lg="3" :$resourceRoute :$action field="step" :value="number_format($supplierQualificationCriterion->step, 2, ',', '.')" :disabled="true" />
                            <x-input.text md="3" lg="3" :$resourceRoute :$action field="weight" :value="number_format($supplierQualificationCriterion->weight, 2, ',', '.')" :disabled="true" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="created_at" :value="$supplierQualificationCriterion->friendly_created_at" :disabled="true" />
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="updated_at" :value="$supplierQualificationCriterion->friendly_updated_at" :disabled="true" />
                        </x-form.row>
                    </x-form.section>
                </x-tabs.tab-content-item>
            </x-slot>
        </x-tabs>
    </x-card>
@endsection
