@extends('layouts.app')

@section('content')
    <x-card :title="__('supplier_qualifications.cards.show.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('supplier_qualifications.index')">
        <x-tabs id="supplier-qualification-show">
            <x-slot name="tabHeaders">
                <x-tabs.tab-header-item id="supplier-qualification-show-general" text="{{ __('supplier_qualifications.forms.sections.general') }}" :active="true" />
                <x-tabs.tab-header-item id="supplier-qualification-show-criteria" text="{{ __('supplier_qualifications.forms.sections.criteria') }}" />
            </x-slot>
            <x-slot name="tabContent">
                <x-tabs.tab-content-item id="supplier-qualification-show-general" :active="true">
                    <x-form.section>
                        <x-form.row>
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="supplier_id" :value="$supplierQualification->supplier->name" :disabled="true" />
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="grade" :value="number_format($supplierQualification->grade, 2, ',', '.')" :disabled="true" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="qualified_at" :value="$supplierQualification->friendly_qualified_at" :disabled="true" />
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="qualification_expires_at" :value="$supplierQualification->friendly_qualification_expires_at" :disabled="true" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="created_at" :value="$supplierQualification->friendly_created_at" :disabled="true" />
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="updated_at" :value="$supplierQualification->friendly_updated_at" :disabled="true" />
                        </x-form.row>
                    </x-form.section>
                </x-tabs.tab-content-item>
                <x-tabs.tab-content-item id="supplier-qualification-show-criteria">
                    <x-form.section>
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle mr-2"></i>
                            Detalhamento dos critérios de qualificação e suas respectivas notas.
                        </div>
                        
                        @if($supplierQualification->supplierQualificationItems->count() > 0)
                            @foreach($supplierQualification->supplierQualificationItems->sortBy('sequence') as $item)
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <strong>{{ $item->supplierQualificationCriterion->name }}</strong>
                                            <span class="badge badge-secondary ml-2">Peso: {{ $item->supplierQualificationCriterion->weight }}</span>
                                            <span class="badge badge-info ml-1">Sequência: {{ $item->sequence }}</span>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <label class="form-label">Nota mínima</label>
                                                <input type="text" class="form-control" value="{{ number_format($item->supplierQualificationCriterion->min_grade, 2, ',', '.') }}" readonly>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Nota máxima</label>
                                                <input type="text" class="form-control" value="{{ number_format($item->supplierQualificationCriterion->max_grade, 2, ',', '.') }}" readonly>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Passo</label>
                                                <input type="text" class="form-control" value="{{ number_format($item->supplierQualificationCriterion->step, 2, ',', '.') }}" readonly>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">Nota atribuída</label>
                                                <input type="text" class="form-control bg-light" value="{{ number_format($item->grade, 2, ',', '.') }}" readonly>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">Nota ponderada</label>
                                                <input type="text" class="form-control bg-success text-white" value="{{ number_format($item->grade * $item->supplierQualificationCriterion->weight, 2, ',', '.') }}" readonly>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <small class="text-muted">
                                                    Cálculo: {{ number_format($item->grade, 2, ',', '.') }} × {{ $item->supplierQualificationCriterion->weight }} = {{ number_format($item->grade * $item->supplierQualificationCriterion->weight, 2, ',', '.') }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach

                            <!-- Final Grade Summary -->
                            <div class="card border-success">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0 text-success">
                                        <strong>Resumo da Qualificação</strong>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="form-label">Total de critérios</label>
                                            <input type="text" class="form-control" value="{{ $supplierQualification->supplierQualificationItems->count() }}" readonly>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Soma dos pesos</label>
                                            <input type="text" class="form-control" value="{{ number_format($supplierQualification->supplierQualificationItems->sum(function($item) { return $item->supplierQualificationCriterion->weight; }), 2, ',', '.') }}" readonly>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Nota final</label>
                                            <input type="text" class="form-control bg-success text-white font-weight-bold" value="{{ number_format($supplierQualification->grade, 2, ',', '.') }}" readonly>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <small class="text-muted">
                                                <strong>Fórmula:</strong> Nota Final = (Σ(Nota × Peso)) / Σ(Peso)<br>
                                                <strong>Cálculo:</strong> 
                                                ({{ $supplierQualification->supplierQualificationItems->map(function($item) { 
                                                    return number_format($item->grade * $item->supplierQualificationCriterion->weight, 2, ',', '.'); 
                                                })->implode(' + ') }}) / 
                                                {{ number_format($supplierQualification->supplierQualificationItems->sum(function($item) { return $item->supplierQualificationCriterion->weight; }), 2, ',', '.') }} = 
                                                {{ number_format($supplierQualification->grade, 2, ',', '.') }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle mr-2"></i>
                                Nenhum critério de qualificação foi encontrado para esta qualificação.
                            </div>
                        @endif
                    </x-form.section>
                </x-tabs.tab-content-item>
            </x-slot>
        </x-tabs>
    </x-card>
@endsection
