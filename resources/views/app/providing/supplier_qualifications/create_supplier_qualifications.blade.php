@extends('layouts.app')

@section('content')
    <x-card :title="__('supplier_qualifications.cards.create.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('supplier_qualifications.index')">
        <x-form.create-form :$resourceRoute>
            <x-tabs id="supplier-qualification-create">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="supplier-qualification-create-general" text="{{ __('supplier_qualifications.forms.sections.general') }}" :active="true" />
                    <x-tabs.tab-header-item id="supplier-qualification-create-criteria" text="{{ __('supplier_qualifications.forms.sections.criteria') }}" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="supplier-qualification-create-general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <div class="col-sm-12 col-md-6 col-lg-6">
                                    <x-input.label :$resourceRoute :$action field="supplier_id" :required="true" />
                                    <select class="form-control" id="supplier_id" name="supplier_id" required>
                                        <option value="">Selecione um fornecedor</option>
                                    </select>
                                </div>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.date md="6" lg="6" :$resourceRoute :$action field="qualified_at" :required="true" />
                                <x-input.date md="6" lg="6" :$resourceRoute :$action field="qualification_expires_at" :required="true" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="supplier-qualification-create-criteria">
                        <x-form.section>
                            <div id="criteria-section">
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle mr-2"></i>
                                    Informe as notas para cada critério de qualificação. A nota final será calculada automaticamente com base nos pesos dos critérios.
                                </div>
                                <div id="criteria-container">
                                    <!-- Criteria will be loaded here dynamically -->
                                </div>
                            </div>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.create-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
    <script>
        let criteria = @json($criteria ?? []);
        
        $(document).ready(function() {
            // Initialize supplier select2
            initializeSelect2(
                'supplier_id',
                "{{ route('suppliers.get_by_name_trading_name_or_tax_id_number') }}",
                'Digite a razão social, o nome fantasia ou o CNPJ de um fornecedor',
                function(item) {
                    return {
                        id: item.id,
                        text: item.name,
                        trading_name: item.trading_name,
                        friendly_tax_id_number: item.friendly_tax_id_number
                    }
                }
            );

            // Load criteria when page loads
            loadCriteria();

            // Update final grade when any criterion grade changes
            $(document).on('input', '.criterion-grade', function() {
                calculateFinalGrade();
            });
        });

        function loadCriteria() {
            let container = $('#criteria-container');
            container.empty();

            if (criteria.length === 0) {
                container.html('<div class="alert alert-warning">Nenhum critério de qualificação foi encontrado. <a href="{{ route("supplier_qualification_criteria.index") }}">Clique aqui para gerenciar critérios</a>.</div>');
                return;
            }

            criteria.forEach(function(criterion, index) {
                let criterionHtml = `
                    <div class="card mb-3 criterion-card" data-criterion-id="${criterion.id}">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <strong>${criterion.name}</strong>
                                <span class="badge badge-secondary ml-2">Peso: ${criterion.weight}</span>
                                <span class="badge badge-info ml-1">Sequência: ${criterion.sequence}</span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Nota mínima</label>
                                    <input type="text" class="form-control" value="${criterion.min_grade}" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Nota máxima</label>
                                    <input type="text" class="form-control" value="${criterion.max_grade}" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Passo</label>
                                    <input type="text" class="form-control" value="${criterion.step}" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Nota atribuída <span class="text-danger">*</span></label>
                                    <input type="number" 
                                           class="form-control criterion-grade" 
                                           name="criteria[${index}][grade]" 
                                           min="${criterion.min_grade}" 
                                           max="${criterion.max_grade}" 
                                           step="${criterion.step}"
                                           data-weight="${criterion.weight}"
                                           required>
                                    <input type="hidden" name="criteria[${index}][criterion_id]" value="${criterion.id}">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-12">
                                    <small class="text-muted">
                                        Nota ponderada: <span class="weighted-grade-display" data-criterion-id="${criterion.id}">0.00</span>
                                        (Nota × Peso: <span class="calculation-display" data-criterion-id="${criterion.id}">0 × ${criterion.weight} = 0.00</span>)
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.append(criterionHtml);
            });

            // Add final grade display
            container.append(`
                <div class="card border-success">
                    <div class="card-header bg-light">
                        <h6 class="mb-0 text-success">
                            <strong>Nota Final Calculada: <span id="final-grade-display">0.00</span></strong>
                        </h6>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            A nota final é calculada como a média ponderada de todos os critérios.
                        </small>
                    </div>
                </div>
            `);
        }

        function calculateFinalGrade() {
            let totalWeightedGrade = 0;
            let totalWeight = 0;

            $('.criterion-grade').each(function() {
                let grade = parseFloat($(this).val()) || 0;
                let weight = parseFloat($(this).data('weight')) || 0;
                let criterionId = $(this).closest('.criterion-card').data('criterion-id');
                
                let weightedGrade = grade * weight;
                totalWeightedGrade += weightedGrade;
                totalWeight += weight;

                // Update individual criterion display
                $(`.weighted-grade-display[data-criterion-id="${criterionId}"]`).text(weightedGrade.toFixed(2));
                $(`.calculation-display[data-criterion-id="${criterionId}"]`).text(`${grade} × ${weight} = ${weightedGrade.toFixed(2)}`);
            });

            let finalGrade = totalWeight > 0 ? (totalWeightedGrade / totalWeight) : 0;
            $('#final-grade-display').text(finalGrade.toFixed(2));
        }
    </script>
@endsection
