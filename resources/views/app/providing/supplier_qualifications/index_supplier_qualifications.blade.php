@extends('layouts.app')

@section('content')
    <x-index-card :$resourceRoute :createsResource="auth()->user()->can(\App\Models\Permission::CREATE_SUPPLIER_QUALIFICATIONS)" :actions="false" :setups="true">
        <x-slot name="setupLinks">
            <x-card.actions.action-link text="Critérios de qualificação" :route="route('supplier_qualification_criteria.index')" />
        </x-slot>

        <livewire:supplier-qualification.tables.supplier-qualifications-table />
    </x-index-card>
@endsection
