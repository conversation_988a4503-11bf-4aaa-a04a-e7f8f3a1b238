@extends('layouts.app')

@section('content')
    <x-card :title="__('provider_companies.cards.create.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('providers.edit', $provider->id)">
        <x-form.create-form :$resourceRoute :routeParameters="$provider->id">
            <x-tabs id="provider-companies-create">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="general" text="Geral" :active="true" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="provider_id" :value="$provider->name" :disabled="true" />
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="provider_trading_name" :value="$provider->trading_name" :disabled="true" />
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="provider_tax_id_number" :value="$provider->friendly_tax_id_number" :disabled="true" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.select md="6" lg="6" :$resourceRoute :$action field="company_id" />
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="company_trading_name" :disabled="true" />
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="company_tax_id_number" :disabled="true" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.create-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
    <script>
        $(document).ready(function() {
            initializeSelect2(
                'company_id',
                "{{ route('companies.get_by_name_trading_name_or_tax_id_number') }}",
                'Digite a razão social, o nome fantasia ou o CNPJ de um cliente',
                function(item) {
                    return {
                        id: item.id,
                        text: item.name,
                        tradingName: item.trading_name,
                        taxIdNumber: item.friendly_tax_id_number
                    }
                }
            );

            $('#company_id').on('select2:select', function(e) {
                $('#company_trading_name').val(e.params.data.tradingName);
                $('#company_tax_id_number').val(e.params.data.taxIdNumber);
            });
        });
    </script>
@endsection
