@extends('layouts.app')

@section('content')
    <x-card :cardHeaderTitle="true" :title="__('provider_city_coverage_cities.cards.create.header.title')" :backButton="true" :backRoute="route('providers.edit', $provider->id)">
        <x-form.create-form :$resourceRoute :routeParameters="$provider->id">
            <x-tabs id="provider-city-coverage-cities-create">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="general" text="Geral" :active="true" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.select md="9" lg="9" :$resourceRoute :$action field="city_coverage_city_id" :required="true" />
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="state_abbreviation" :disabled="true" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.create-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>

    <script>
        $(document).ready(function() {
            initializeSelect2(
                'city_coverage_city_id',
                "{{ route('cities.get_by_name') }}",
                'Digite o nome de uma cidade',
                function(item) {
                    return {
                        id: item.id,
                        text: item.name,
                        state_abbreviation: item.state.abbreviation
                    }
                }
            );

            $('#city_coverage_city_id').on('select2:select', function(e) {
                $('#state_abbreviation').val(e.params.data.state_abbreviation);
            });
        });
    </script>
@endsection
