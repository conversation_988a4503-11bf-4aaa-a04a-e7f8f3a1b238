@extends('layouts.app')

@section('content')
    <x-card :cardHeaderTitle="true" :title="__('provider_city_coverage_cities.cards.show.header.title')" :backButton="true">
        <x-form.edit-form :$resourceRoute :routeParameters="[$provider->id, $providerCityCoverageCity->id]" :disabled="true">
            <x-tabs id="provider-city-coverage-cities-edit">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="general" text="Geral" :active="true" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.select md="9" lg="9" :$resourceRoute :$action field="city_coverage_city_id">
                                    <option value="{{ $providerCityCoverageCity->city_coverage_city_id }}">{{ $providerCityCoverageCity->cityCoverageCity->name }}</option>
                                </x-input.select>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="state_abbreviation" :disabled="true" :value="$providerCityCoverageCity->cityCoverageCity->state->abbreviation" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.edit-form>
    </x-card>
@endsection
