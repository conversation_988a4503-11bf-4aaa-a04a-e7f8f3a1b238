@extends('layouts.app')

@section('content')
    <x-form.edit-form :$resourceRoute :routeParameters="[$provider->id, $providerContact->id]">
        <x-card :title="__('provider_contacts.cards.edit.header.title')" :cardHeaderTitle="true" :backButton="true">
            <x-form.row>
                <x-input.checkbox :$resourceRoute :$action field="main" :checked="$providerContact->main" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.select md="3" lg="3" :$resourceRoute :$action field="type" :required="true">
                    <option value="">Escolha uma opção</option>
                    @foreach ($types as $key => $value)
                        <option value="{{ $key }}" @if ($key === $providerContact->type) selected @endif>{{ $value }}</option>
                    @endforeach
                </x-input.select>
                <x-input.text md="9" lg="9" :$resourceRoute :$action field="name" :required="true" :value="$providerContact->name" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="3" lg="3" :$resourceRoute :$action field="phone_1" customClass="phone-input" :value="$providerContact->phone_1" />
                <x-input.text md="3" lg="3" :$resourceRoute :$action field="phone_2" customClass="phone-input" :value="$providerContact->phone_2" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="email" :value="$providerContact->email" />
            </x-form.row>
        </x-card>
    </x-form.edit-form>
@endsection

@section('js-scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>

    <script>
        $(document).ready(function() {
            const SPMaskBehavior = function (val) {
                return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
            };

            const spOptions = {
                onKeyPress: function (val, e, field, options) {
                    field.mask(SPMaskBehavior.apply({}, arguments), options);
                }
            };

            $('.phone-input').mask(SPMaskBehavior, spOptions);
        });
    </script>
@endsection
