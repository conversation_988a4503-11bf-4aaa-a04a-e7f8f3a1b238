@extends('layouts.app')

@section('content')
    <x-form.create-form :$resourceRoute :routeParameters="$provider->id">
        <x-card :title="__('provider_contacts.cards.create.header.title')" :cardHeaderTitle="true" :backButton="true">
            <x-form.row>
                <x-input.checkbox :$resourceRoute :$action field="main" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.select md="3" lg="3" :$resourceRoute :$action field="type" :required="true">
                    <option value="">Escolha uma opção</option>
                    @foreach ($types as $key => $value)
                        <option value="{{ $key }}">{{ $value }}</option>
                    @endforeach
                </x-input.select>
                <x-input.text md="9" lg="9" :$resourceRoute :$action field="name" :required="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="3" lg="3" :$resourceRoute :$action field="phone_1" customClass="phone-input" />
                <x-input.text md="3" lg="3" :$resourceRoute :$action field="phone_2" customClass="phone-input" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="email" />
            </x-form.row>
        </x-card>
    </x-form.create-form>
@endsection

@section('js-scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>

    <script>
        $(document).ready(function() {
            const SPMaskBehavior = function (val) {
                return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
            };

            const spOptions = {
                onKeyPress: function (val, e, field, options) {
                    field.mask(SPMaskBehavior.apply({}, arguments), options);
                }
            };

            $('.phone-input').mask(SPMaskBehavior, spOptions);
        });
    </script>
@endsection
