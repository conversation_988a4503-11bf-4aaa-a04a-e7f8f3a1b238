@extends('layouts.app')

@section('content')
    <x-card :title="__('provider_qualification_criteria.cards.create.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('provider_qualification_criteria.index')">
        <x-form.create-form :$resourceRoute>
            <x-tabs id="provider-qualification-criterion-create">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="provider-qualification-criterion-create-general" text="Geral" :active="true" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="provider-qualification-criterion-create-general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :required="true" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="sequence" :required="true" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="min_grade" :required="true" step="0.01" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="max_grade" :required="true" step="0.01" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="step" :required="true" step="0.01" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="weight" :required="true" step="0.01" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.create-form>
    </x-card>
@endsection
