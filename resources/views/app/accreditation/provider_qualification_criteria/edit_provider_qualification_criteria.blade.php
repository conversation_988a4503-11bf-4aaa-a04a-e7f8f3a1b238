@extends('layouts.app')

@section('content')
    <x-card :title="__('provider_qualification_criteria.cards.edit.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('provider_qualification_criteria.index')">
        <x-form.edit-form :$resourceRoute :routeParameters="$providerQualificationCriterion->id">
            <x-tabs id="provider-qualification-criterion-edit">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="provider-qualification-criterion-edit-general" text="Geral" :active="true" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="provider-qualification-criterion-edit-general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :required="true" :value="$providerQualificationCriterion->name" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="sequence" :required="true" :value="$providerQualificationCriterion->sequence" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="min_grade" :required="true" step="0.01" :value="$providerQualificationCriterion->min_grade" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="max_grade" :required="true" step="0.01" :value="$providerQualificationCriterion->max_grade" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="step" :required="true" step="0.01" :value="$providerQualificationCriterion->step" />
                                <x-input.number md="3" lg="3" :$resourceRoute :$action field="weight" :required="true" step="0.01" :value="$providerQualificationCriterion->weight" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.edit-form>
    </x-card>
@endsection
