@extends('layouts.app')

@section('content')
    <x-card :title="__('provider_qualification_criteria.cards.show.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('provider_qualification_criteria.index')">
        <x-tabs id="provider-qualification-criterion-show">
            <x-slot name="tabHeaders">
                <x-tabs.tab-header-item id="provider-qualification-criterion-show-general" text="Geral" :active="true" />
            </x-slot>
            <x-slot name="tabContent">
                <x-tabs.tab-content-item id="provider-qualification-criterion-show-general" :active="true">
                    <x-form.section>
                        <x-form.row>
                            <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :value="$providerQualificationCriterion->name" :disabled="true" />
                            <x-input.number md="3" lg="3" :$resourceRoute :$action field="sequence" :value="$providerQualificationCriterion->sequence" :disabled="true" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.number md="3" lg="3" :$resourceRoute :$action field="min_grade" step="0.01" :value="$providerQualificationCriterion->min_grade" :disabled="true" />
                            <x-input.number md="3" lg="3" :$resourceRoute :$action field="max_grade" step="0.01" :value="$providerQualificationCriterion->max_grade" :disabled="true" />
                            <x-input.number md="3" lg="3" :$resourceRoute :$action field="step" step="0.01" :value="$providerQualificationCriterion->step" :disabled="true" />
                            <x-input.number md="3" lg="3" :$resourceRoute :$action field="weight" step="0.01" :value="$providerQualificationCriterion->weight" :disabled="true" />
                        </x-form.row>
                    </x-form.section>
                </x-tabs.tab-content-item>
            </x-slot>
        </x-tabs>
    </x-card>
@endsection
