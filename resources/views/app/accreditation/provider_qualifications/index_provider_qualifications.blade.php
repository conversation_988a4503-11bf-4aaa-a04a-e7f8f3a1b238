@extends('layouts.app')

@section('content')
    <x-index-card :$resourceRoute :createsResource="auth()->user()->can(\App\Models\Permission::CREATE_PROVIDER_QUALIFICATIONS)" :actions="false" :setups="true">
        <x-slot name="setupLinks">
            <x-card.actions.action-link text="Critérios de qualificação" :route="route('provider_qualification_criteria.index')" />
        </x-slot>

        <livewire:provider-qualification.tables.provider-qualifications-table />
    </x-index-card>
@endsection
