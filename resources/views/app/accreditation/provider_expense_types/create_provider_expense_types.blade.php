@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-12">
            @include('base.components.card.edit.header', ['route' => route('providers.edit', $provider->id)])
            <div class="card shadow-sm">
                <div class="card-body">
                    <form class="form-group" method="post" action="{{ route('provider_expense_types.store', $provider->id) }}">
                        <fieldset>
                            @csrf
                            @include('base.components.error_alert')

                            <div class="row">
                                <x-input.select md="6" lg="6" resourceRoute="provider_expense_types" action="create" field="expense_type_id" disabled="true">
                                    <option value="{{ $provider->id }}">{{ $provider->name }}</option>
                                </x-input.select>
                                <x-input.select md="6" lg="6" resourceRoute="provider_expense_types" action="create" field="expense_type_id">
                                    @foreach ($expenseTypes as $expenseType)
                                        <option value="{{ $expenseType->id }}">{{ $expenseType->name }}</option>
                                    @endforeach
                                </x-input.select>
                            </div>
                        </fieldset>

                        <x-button.save />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
