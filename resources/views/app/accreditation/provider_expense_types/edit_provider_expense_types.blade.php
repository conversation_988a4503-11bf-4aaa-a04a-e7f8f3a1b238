@extends('layouts.app')

@section('content')
    <x-action-card :resourceRoute="$resourceRoute" :action="$action" :backRoute="route('providers.edit', $provider->id)">
        <x-form.edit-form :resourceRoute="$resourceRoute" :routeParameters="[$provider, $providerExpenseType]" :id="$providerExpenseType->id">
            <x-form.row>
                <x-input.select md="6" lg="6" :resourceRoute="$resourceRoute" :action="$action" field="expense_type_id" disabled="true">
                    <option value="{{ $provider->id }}">{{ $provider->name }}</option>
                </x-input.select>
                <x-input.select md="6" lg="6" :resourceRoute="$resourceRoute" :action="$action" field="expense_type_id" required="true">
                    @foreach ($expenseTypes as $expenseType)
                        <option value="{{ $expenseType->id }}" @if ($expenseType->id === $providerExpenseType->expense_type_id) selected @endif >{{ $expenseType->name }}</option>
                    @endforeach
                </x-input.select>
            </x-form.row>
        </x-form.edit-form>
    </x-action-card>
@endsection
