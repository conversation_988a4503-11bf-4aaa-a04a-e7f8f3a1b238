@extends('layouts.app')

@section('content')
    <x-action-card :resourceRoute="$resourceRoute" :action="$action" :backRoute="route('providers.edit', $provider->id)">
        <x-form.edit-form :resourceRoute="$resourceRoute" :id="$providerExpenseType->id" :disabled="true">
            <x-form.row>
                <x-input.select md="6" lg="6" :resourceRoute="$resourceRoute" :action="$action" field="expense_type_id" disabled="true">
                    <option value="{{ $provider->id }}">{{ $provider->name }}</option>
                </x-input.select>
                <x-input.select md="6" lg="6" :resourceRoute="$resourceRoute" :action="$action" field="expense_type_id" required="true">
                    <option value="{{ $providerExpenseType->id }}">{{ $providerExpenseType->expenseType->name }}</option>
                </x-input.select>
            </x-form.row>
        </x-form.edit-form>
    </x-action-card>
@endsection
