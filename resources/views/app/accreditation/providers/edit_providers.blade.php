@extends('layouts.app')

@section('content')
    <x-card :title="__('providers.cards.edit.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('providers.index')" :actions="true">
        <x-slot name="badges">
            @if ($provider->erp_flex_id)
                <div>
                    <span class="badge badge-primary px-2 py-1">ID ERPFlex: {{ $provider->erp_flex_id }}</span>
                </div>
            @endif
            @if ($provider->soc_id)
                <div>
                    <span class="badge badge-info px-2 py-1">ID SOC: {{ $provider->soc_id }}</span>
                </div>
            @endif
            @if ($provider->clm_online_id)
                <div>
                    <span class="badge badge-secondary px-2 py-1">ID CLM Online: {{ $provider->clm_online_id }}</span>
                </div>
            @endif
        </x-slot>

        <x-slot name="actionLinks">
            <h6 class="dropdown-header">Integrações</h6>
            <button type="button" class="dropdown-item" data-toggle="modal" data-target="#create-in-erp-flex-modal" @if ($provider->erp_flex_id) disabled @endif>Enviar para o ERPFlex</button>
            <button type="button" class="dropdown-item" data-toggle="modal" data-target="#create-in-soc-modal" @if ($provider->soc_id) disabled @endif>Enviar para o SOC</button>
            @if ($clmOnlineIntegrationExists)
                <button type="button" class="dropdown-item" data-toggle="modal" data-target="#force-integration-to-clm-online-modal" >Forçar integração para o CLM Online</button>
            @endif
        </x-slot>

        <x-form.edit-form :$resourceRoute :routeParameters="$provider->id" :hasFile="true">
            <x-tabs id="provider-edit">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="provider-edit-documents" text="Documentos" :active="true" />
                    <x-tabs.tab-header-item id="provider-edit-general" text="Geral" />
                    <x-tabs.tab-header-item id="provider-edit-address" text="Endereço" />
                    <x-tabs.tab-header-item id="provider-edit-contacts" text="Contatos" />
                    <x-tabs.tab-header-item id="provider-edit-treatment-info" text="Informações de atendimento" />
                    <x-tabs.tab-header-item id="provider-edit-service-provision" text="Prestação de serviços" />
                    <x-tabs.tab-header-item id="provider-edit-banking-info" text="Informações bancárias" />
                    <x-tabs.tab-header-item id="provider-edit-city-coverage-cities" text="Abrangência (cidades)" />
                    <x-tabs.tab-header-item id="provider-edit-companies" text="Clientes" />
                    <x-tabs.tab-header-item id="provider-edit-procedures" text="Procedimentos" />
                    <x-tabs.tab-header-item id="provider-edit-expense-types" text="Tipos de despesas" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="provider-edit-documents" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <div class="ml-auto pr-3 pt-3">
                                    <button id="addFileBtn" class="btn btn-primary btn-sm shadow-sm" type="button"><i class="fa fa-plus"></i></button>
                                    <button id="removeFileBtn" class="btn btn-danger btn-sm shadow-sm" type="button"><i class="fa fa-minus"></i></button>
                                </div>
                            </x-form.row>
                            <x-form.row :marginTop="true" customClass="file-group">
                                <div class="col-12 file-group-item">
                                    <input type="file" name="files[]" class="input-group control-group form-control file-input mt-2">
                                </div>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <div class="col-12">
                                    <livewire:provider-file.tables.provider-files-table :providerId="$provider->id" />
                                </div>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-general">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="tax_id_number" value="{{ $provider->tax_id_number }}" maxLength="14" required="true"></x-input.text>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" value="{{ $provider->name }}" required="true"></x-input.text>
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="trading_name" value="{{ $provider->trading_name }}" required="true"></x-input.text>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="national_simple_tax">
                                    <option value="1" @if ($provider->national_simple_tax) selected @endif>Sim</option>
                                    <option value="0" @if (!$provider->national_simple_tax) selected @endif>Não</option>
                                </x-input.select>
                                <div class="col-sm-12 col-md-3 col-lg-3">
                                    <div class="d-flex justify-content-between">
                                        <label for="state_registration_no" class="mb-1">Inscrição estadual</label>
                                        <div>
                                            <input type="checkbox" name="exempt_from_state_registration" id="exempt_from_state_registration" @if ($provider->state_registration_no === 'ISENTO') checked @endif> Isento?
                                        </div>
                                    </div>
                                    <input type="text" id="state_registration_no" name="state_registration_no" class="form-control" value="{{ $provider->state_registration_no }}" @if ($provider->state_registration_no === 'ISENTO') readonly @endif />
                                </div>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="city_registration_no" value="{{ $provider->city_registration_no }}" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-address">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="zipcode" value="{{ $provider->zipcode }}" required="true"></x-input.text>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="address" value="{{ $provider->address }}" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="number" value="{{ $provider->number }}" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="additional_info" value="{{ $provider->additional_info }}"></x-input.text>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="district" value="{{ $provider->district }}" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="city" value="{{ $provider->city }}" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="state" value="{{ $provider->state }}" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="country" readonly="true" value="BR"></x-input.text>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-contacts">
                        <x-form.section :marginTop="true" :createsResource="true" :createResourceRoute="route('provider_contacts.create', $provider->id)">
                            <livewire:provider-contact.tables.provider-contacts-table :providerId="$provider->id" />
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-treatment-info">
                        <x-form.section>
                            <x-form.row>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="treatment_type">
                                    <option value="{{ \App\Models\Provider::TREATMENT_TYPE_APPOINTMENT }}" @if ($provider->treatment_type === \App\Models\Provider::TREATMENT_TYPE_APPOINTMENT) selected @endif>Horário agendado</option>
                                    <option value="{{ \App\Models\Provider::TREATMENT_TYPE_ARRIVAL_ORDER }}" @if ($provider->treatment_type === \App\Models\Provider::TREATMENT_TYPE_ARRIVAL_ORDER) selected @endif>Ordem de chegada</option>
                                </x-input.select>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="advance_payment">
                                    <option value="0" @if (!$provider->advance_payment) selected @endif>Não</option>
                                    <option value="1" @if ($provider->advance_payment) selected @endif>Sim</option>
                                </x-input.select>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="is_schedule_managed">
                                    <option value="0" @if (!$provider->is_schedule_managed) selected @endif>Não</option>
                                    <option value="1" @if ($provider->is_schedule_managed) selected @endif>Sim</option>
                                </x-input.select>
                            </x-form.row>
                            <div id="provider-time-div">
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="monday_starting_time" value="{{ $provider->monday_starting_time }}"></x-input.time>
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="monday_ending_time" value="{{ $provider->monday_ending_time }}"></x-input.time>
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="tuesday_starting_time" value="{{ $provider->tuesday_starting_time }}"></x-input.time>
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="tuesday_ending_time" value="{{ $provider->tuesday_ending_time }}"></x-input.time>
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="wednesday_starting_time" value="{{ $provider->wednesday_starting_time }}"></x-input.time>
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="wednesday_ending_time" value="{{ $provider->wednesday_ending_time }}"></x-input.time>
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="thursday_starting_time" value="{{ $provider->thursday_starting_time }}"></x-input.time>
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="thursday_ending_time" value="{{ $provider->thursday_ending_time }}"></x-input.time>
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="friday_starting_time" value="{{ $provider->friday_starting_time }}"></x-input.time>
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="friday_ending_time" value="{{ $provider->friday_ending_time }}"></x-input.time>
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="saturday_starting_time" value="{{ $provider->saturday_starting_time }}"></x-input.time>
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="saturday_ending_time" value="{{ $provider->saturday_ending_time }}"></x-input.time>
                                </x-form.row>
                            </div>
                            <x-form.row :marginTop="true">
                                <x-input.textarea :$resourceRoute :$action field="treatment_additional_info" :rows="10" :value="$provider->treatment_additional_info" :maxLength="1000" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-service-provision">
                        <x-form.section>
                            <x-form.row>
                                <x-input.date md="3" lg="3" :$resourceRoute :$action field="service_provision_started_at" :value="$provider->service_provision_started_at" />
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="qualified">
                                    <option value="1" @if ($provider->qualified) selected @endif>Sim</option>
                                    <option value="0" @if (!$provider->qualified) selected @endif>Não</option>
                                </x-input.select>
                                <x-input.date md="3" lg="3" :$resourceRoute :$action field="qualification_expires_at" value="{{ $provider->qualification_expires_at }}"></x-input.date>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-banking-info">
                        <x-form.section>
                            <x-form.row>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="payment_term">
                                    <option value="">Escolha um</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_15_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_15_DAYS) selected @endif>15 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_31_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_31_DAYS) selected @endif>31 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_45_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_45_DAYS) selected @endif>45 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_60_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_60_DAYS) selected @endif>60 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_75_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_75_DAYS) selected @endif>75 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_90_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_90_DAYS) selected @endif>90 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_120_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_120_DAYS) selected @endif>120 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_150_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_150_DAYS) selected @endif>150 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_180_DAYS }}" @if ($provider->payment_term === \App\Models\Contract::PAYMENT_TERM_180_DAYS) selected @endif>180 dias</option>
                                </x-input.select>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="payment_day">
                                    <option value="" selected disabled>Escolha um dia</option>
                                    @for ($i = 1; $i <= 31; $i++)
                                        <option value="{{ $i }}" @if ($i === $provider->payment_day) selected @endif>{{ $i }}</option>
                                    @endfor
                                </x-input.select>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="week_expiration_day">
                                    <option value="">Escolha um</option>
                                    <option value="{{ \Carbon\Carbon::SUNDAY }}" @if ($provider->week_expiration_day === \Carbon\Carbon::SUNDAY) selected @endif>Domingo</option>
                                    <option value="{{ \Carbon\Carbon::MONDAY }}" @if ($provider->week_expiration_day === \Carbon\Carbon::MONDAY) selected @endif>Segunda-feira</option>
                                    <option value="{{ \Carbon\Carbon::TUESDAY }}" @if ($provider->week_expiration_day === \Carbon\Carbon::TUESDAY) selected @endif>Terça-feira</option>
                                    <option value="{{ \Carbon\Carbon::WEDNESDAY }}" @if ($provider->week_expiration_day === \Carbon\Carbon::WEDNESDAY) selected @endif>Quarta-feira</option>
                                    <option value="{{ \Carbon\Carbon::THURSDAY }}" @if ($provider->week_expiration_day === \Carbon\Carbon::THURSDAY) selected @endif>Quinta-feira</option>
                                    <option value="{{ \Carbon\Carbon::FRIDAY }}" @if ($provider->week_expiration_day === \Carbon\Carbon::FRIDAY) selected @endif>Sexta-feira</option>
                                    <option value="{{ \Carbon\Carbon::SATURDAY }}" @if ($provider->week_expiration_day === \Carbon\Carbon::SATURDAY) selected @endif>Sábado</option>
                                </x-input.select>
                                <x-input.select md="3" lg="3" resourceRoute="providers" :$action field="preferred_earning_method">
                                    <option value="" disabled selected>Escolha um...</option>
                                    <option value="cash" @if ($provider->preferred_earning_method === 'cash') selected @endif>Dinheiro</option>
                                    <option value="transfer" @if ($provider->preferred_earning_method === 'transfer') selected @endif>Transferência</option>
                                    <option value="pix" @if ($provider->preferred_earning_method === 'pix') selected @endif>PIX</option>
                                </x-input.select>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.select md="6" lg="6" resourceRoute="providers" action="show" field="bank_id">
                                    <option value="{{ $provider->bank_id }}">{{ $provider->bank?->name }}</option>
                                </x-input.select>
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="bank_code" disabled="true" value="{{ $provider->bank?->code }}" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="branch_number" value="{{ $provider->branch_number }}" />
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="branch_digit" value="{{ $provider->branch_digit }}" />
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="account_number" value="{{ $provider->account_number }}" />
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="account_digit" value="{{ $provider->account_digit }}" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text resourceRoute="providers" action="show" field="pix_1" value="{{ $provider->pix_1 }}" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text resourceRoute="providers" action="show" field="pix_2" value="{{ $provider->pix_2 }}" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text resourceRoute="providers" action="show" field="pix_3" value="{{ $provider->pix_3 }}" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-city-coverage-cities">
                        <x-form.section :marginTop="true" :createsResource="true" :createResourceRoute="route('provider_city_coverage_cities.create', $provider->id)">
                            <livewire:provider-city-coverage-city.tables.provider-city-coverage-cities-table :providerId="$provider->id" />
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-companies">
                        <x-form.section :marginTop="true" :createsResource="true" :createResourceRoute="route('provider_companies.create', $provider->id)">
                            <livewire:provider-company.tables.provider-companies-table :providerId="$provider->id" />
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-procedures">
                        <x-form.section :marginTop="true" :createsResource="true" :createResourceRoute="route('provider_procedures.create', $provider->id)">
                            <livewire:provider-procedure.tables.provider-procedures-table :providerId="$provider->id" />
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-edit-expense-types">
                        <x-form.section :marginTop="true" :createsResource="true" :createResourceRoute="route('provider_expense_types.create', $provider->id)">
                            <livewire:provider-expense-type.tables.provider-expense-types-table :providerId="$provider->id" />
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.edit-form>
    </x-card>

    <x-modal.default-action title="Importar template de tabela padrão do SOC" id="import-default-table-template" :route="route('provider_procedures.import_default_table_template', $provider->id)">
        <p>Deseja importar o template de tabela padrão?</p>
        <p>
            <strong>Lembre-se:</strong> ao continuar, os procedimentos já existentes <strong>não</strong> serão sobrescritos,
            e somente serão criados os procedimentos mapeados no SOC, <strong>com valor zerado.</strong>
            Você será responsável por alimentar os valores dos exames no credenciado.
        </p>
    </x-modal.default-action>

    <div class="modal fade" id="address-delete-modal" tabindex="-1" role="dialog" aria-labelledby="address-delete-label" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form id="address-delete-form" method="POST">
                    @csrf
                    @method('DELETE')
                    <div class="modal-header">
                        <h5 class="modal-title" id="address-delete-label">{{ __('Confirmar exclusão de endereço') }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-primary" data-dismiss="modal">{{ __('buttons.no') }}</button>
                        <button type="submit" class="btn btn-danger">{{ __('buttons.yes') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <x-modal.delete resourceRoute="provider_expense_types" prefix="provider-expense-type" />
    <x-modal.delete resourceRoute="provider_procedures" />

    <x-modal.default-action id="create-in-erp-flex" title="Criar no ERPFlex" :route="route('providers.create_in_erp_flex', $provider->id)">
        <span>Deseja criar o cadastro no ERPFlex?</span>
    </x-modal.default-action>

    <x-modal.default-action id="create-in-soc" title="Criar no SOC" :route="route('providers.create_in_soc', $provider->id)">
        <span>Deseja criar o cadastro no SOC?</span>
    </x-modal.default-action>

    <x-modal.default-action id="force-integration-to-clm-online" title="Forçar integração para o CLM Online" :route="route('providers.force_integration_to_clm_online', $provider->id)">
        <span>Deseja forçar a integração para o CLM Online?</span>
    </x-modal.default-action>

    @include('layouts.datatables_scripts')
@endsection

@section('js-scripts')
    <script src="{{ global_asset('js/core/components/data-tables/data-tables-helpers.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
    <script>
        $(document).ready(function() {
            const SPMaskBehavior = function(val) {
                return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
            };

            const spOptions = {
                onKeyPress: function(val, e, field, options) {
                    field.mask(SPMaskBehavior.apply({}, arguments), options);
                }
            };

            const taxIdNumberOptions = {
                onKeyPress: function(cpf, ev, el, op) {
                    var masks = ['000.000.000-000', '00.000.000/0000-00'];
                    $('.tax-id-number-input').mask((cpf.length > 14) ? masks[1] : masks[0], op);
                }
            };

            $('#zipcode').mask('00000-000');
            $('#number').mask('99999');

            $('.phone-input').mask(SPMaskBehavior, spOptions);
            $('.tax-id-number-input').mask('000.000.000-00', spOptions);

            $('#tax_id_number').blur(function() {
                $.ajax({
                    url: `https://www.receitaws.com.br/v1/cnpj/${this.value}`,
                    success: function(response) {
                        $('#name').attr('readonly', response.nome !== '');
                        $('#trading_name').attr('readonly', response.fantasia !== '');

                        $('#name').val(response.nome);
                        $('#trading_name').val(response.fantasia);
                    }
                });
            });

            $('#exempt_from_state_registration').click(() => {
                if ($('#exempt_from_state_registration').is(':checked')) {
                    $('#state_registration_no').val('ISENTO');
                    $('#state_registration_no').prop('readonly', true);
                } else {
                    $('#state_registration_no').val(null);
                    $('#state_registration_no').removeAttr('readonly');
                }
            });

            $('#zipcode').blur(function() {
                $.ajax({
                    url: `https://viacep.com.br/ws/${this.value}/json/`,
                    success: function(response) {
                        if (response.logradouro !== '') {
                            $('#address').prop('readonly', true);
                            $('#number').focus();
                        } else {
                            $('#address').prop('readonly', false);
                            $('#address').focus();
                        }

                        $('#additional_info').attr('readonly', response.complemento !== '');
                        $('#district').attr('readonly', response.bairro !== '');
                        $('#city').attr('readonly', response.localidade !== '');
                        $('#state').attr('readonly', response.uf !== '');
                        $('#additional_info').val(response.complemento);
                        $('#address').val(response.logradouro);
                        $('#district').val(response.bairro);
                        $('#city').val(response.localidade);
                        $('#state').val(response.uf);
                    }
                });
            });

            initializeDataTable('address-list', "{{ route('provider_addresses.index', $provider->id) }}", [{
                    data: 'zipcode',
                    name: 'zipcode'
                },
                {
                    data: 'address',
                    name: 'address'
                },
                {
                    data: 'number',
                    name: 'number'
                },
                {
                    data: 'actions',
                    name: 'actions'
                },
            ], {
                manage: true
            });

            initializeDataTable('expense-type-list', "{{ route('provider_expense_types.index', $provider->id) }}", [{
                    data: 'expense_type.name',
                    name: 'expenseType.name'
                },
                {
                    data: 'actions',
                    name: 'actions'
                },
            ], {
                manage: true
            });

            initializeDataTable('procedure-list', "{{ route('provider_procedures.index', $provider->id) }}", [{
                    data: 'name',
                    name: 'procedures.name'
                },
                {
                    data: 'amount',
                    name: 'amount'
                },
                {
                    data: 'actions',
                    name: 'actions'
                },
            ], {
                manage: true
            });

            initializeSelect2(
                'bank_id',
                "{{ route('banks.get_by_code_or_name') }}",
                'Digite o código ou o nome de um banco',
                function(item) {
                    return {
                        id: item.id,
                        text: item.name,
                        code: item.code
                    };
                }
            );

            $('#bank_id').on('select2:select', function(e) {
                $('#bank_code').val(e.params.data.code);
            });

            $('#addFileBtn').click(function() {
                $('.file-group').append($('.file-group-item').last().clone());
                $('.input-group.control-group').last().val(null);
            });

            $('#removeFileBtn').click(function() {
                const fileGroupItemSelector = $('.file-group-item');

                if (fileGroupItemSelector.length > 1) {
                    fileGroupItemSelector.last().remove();
                } else {
                    $('.input-group.control-group').val(null);
                }
            });

            $(document).on('show.bs.modal', function(event) {
                let button = $(event.relatedTarget);

                if (button.data('target') === '#delete-modal') {
                    let name = button.data("provider-procedure-name");
                    let link = button.data("provider-procedure-destroy-link");

                    let modal = $(this);
                    modal.find('.modal-body').text('Deseja excluir o vínculo para o procedimento ' + name + '?');
                    modal.find('#delete-form').attr('action', link);
                } else if (button.data('target') === '#provider-expense-type-delete-modal') {
                    let name = button.data("provider-expense-type-name");
                    let link = button.data("provider-expense-type-destroy-link");

                    let modal = $(this);
                    modal.find('.modal-body').text('Deseja excluir o vínculo para o tipo de despesa ' + name + '?');
                    modal.find('#provider-expense-type-delete-form').attr('action', link);
                } else if (button.data('target') === '#address-delete-modal') {
                    let name = button.data("provider-address-address");
                    let link = button.data("provider-address-destroy-link");

                    let modal = $(this);
                    modal.find('.modal-body').text('Deseja excluir o endereço ' + name + '?');
                    modal.find('#address-delete-form').attr('action', link);
                } else if (button.data('target') === '#delete-file-modal') {
                    let name = button.data("provider-file-filename");
                    let link = button.data("provider-file-destroy-link");

                    let modal = $(this);
                    modal.find('.modal-body').text('Deseja excluir o arquivo ' + name + '?');
                    modal.find('#delete-form').attr('action', link);
                }
            });
        });
    </script>
@endsection
