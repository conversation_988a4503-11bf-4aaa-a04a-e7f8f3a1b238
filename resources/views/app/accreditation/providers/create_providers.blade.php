@extends('layouts.app')

@section('content')
    <x-card :title="__('providers.cards.create.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('providers.index')" >
        <x-form.create-form :$resourceRoute :hasFile="true">
            <x-tabs id="provider-create">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="provider-create-documents" text="Documentos" :active="true" />
                    <x-tabs.tab-header-item id="provider-create-general" text="Geral" />
                    <x-tabs.tab-header-item id="provider-create-address" text="Endereço" />
                    <x-tabs.tab-header-item id="provider-create-contacts" text="Contatos" />
                    <x-tabs.tab-header-item id="provider-create-treatment-info" text="Informações de atendimento" />
                    <x-tabs.tab-header-item id="provider-create-service-provision" text="Prestação de serviços" />
                    <x-tabs.tab-header-item id="provider-create-banking-info" text="Informações bancárias" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="provider-create-documents" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <div class="col-12">
                                    <div class="alert alert-danger">
                                        <span><strong>IMPORTANTE:</strong> não se esqueça de enviar os arquivos do credenciado.</span>
                                    </div>
                                </div>
                            </x-form.row>
                            <x-form.row>
                                <div class="ml-auto pr-3 pt-3">
                                    <button id="addFileBtn" class="btn btn-primary btn-sm shadow-sm" type="button"><i class="fa fa-plus"></i></button>
                                    <button id="removeFileBtn" class="btn btn-danger btn-sm shadow-sm" type="button"><i class="fa fa-minus"></i></button>
                                </div>
                            </x-form.row>
                            <x-form.row :marginTop="true" customClass="file-group">
                                <div class="col-12 file-group-item">
                                    <input type="file" name="files[]" class="input-group control-group form-control file-input mt-2">
                                </div>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-create-general">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="tax_id_number" maxLength="14" required="true" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" required="true" />
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="trading_name" required="true" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="national_simple_tax">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                </x-input.select>
                                <div class="col-sm-12 col-md-3 col-lg-3">
                                    <div class="d-flex justify-content-between">
                                        <label for="state_registration_no" class="mb-1">Inscrição estadual</label>
                                        <div>
                                            <input type="checkbox" name="exempt_from_state_registration" id="exempt_from_state_registration"> Isento?
                                        </div>
                                    </div>
                                    <input type="text" id="state_registration_no" name="state_registration_no" class="form-control" />
                                </div>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="city_registration_no" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-create-address">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="zipcode" required="true"></x-input.text>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="address" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="number" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="additional_info"></x-input.text>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="district" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="city" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="state" required="true"></x-input.text>
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="country" readonly="true" value="BR"></x-input.text>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-create-contacts">
                        <x-form.section>
                            <x-form.row>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="contact_type">
                                    <option value="">Escolha uma opção</option>
                                    @foreach ($types as $key => $value)
                                        <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                </x-input.select>
                                <x-input.text md="9" lg="9" :$resourceRoute :$action field="contact_name" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="contact_phone_1" customClass="phone-input" />
                                <x-input.text md="3" lg="3" :$resourceRoute :$action field="contact_phone_2" customClass="phone-input" />
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="contact_email" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-create-treatment-info">
                        <x-form.section>
                            <x-form.row>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="treatment_type">
                                    <option value="{{ \App\Models\Provider::TREATMENT_TYPE_APPOINTMENT }}">Horário agendado</option>
                                    <option value="{{ \App\Models\Provider::TREATMENT_TYPE_ARRIVAL_ORDER }}">Ordem de chegada</option>
                                </x-input.select>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="advance_payment">
                                    <option value="0">Não</option>
                                    <option value="1">Sim</option>
                                </x-input.select>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="is_schedule_managed">
                                    <option value="0">Não</option>
                                    <option value="1">Sim</option>
                                </x-input.select>
                            </x-form.row>
                            <div id="provider-time-div">
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="monday_starting_time" />
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="monday_ending_time" />
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="tuesday_starting_time" />
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="tuesday_ending_time" />
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="wednesday_starting_time" />
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="wednesday_ending_time" />
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="thursday_starting_time" />
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="thursday_ending_time" />
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="friday_starting_time" />
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="friday_ending_time" />
                                </x-form.row>
                                <x-form.row :marginTop="true">
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="saturday_starting_time" />
                                    <x-input.time md="6" lg="6" :$resourceRoute :$action field="saturday_ending_time" />
                                </x-form.row>
                            </div>
                            <x-form.row :marginTop="true">
                                <x-input.textarea :$resourceRoute :$action field="treatment_additional_info" :rows="10" :maxLength="1000" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-create-service-provision">
                        <x-form.section>
                            <x-form.row>
                                <x-input.date md="3" lg="3" :$resourceRoute :$action field="service_provision_started_at" />
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="qualified">
                                    <option value="1">Sim</option>
                                    <option value="0">Não</option>
                                </x-input.select>
                                <x-input.date md="3" lg="3" :$resourceRoute :$action field="qualification_expires_at" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="provider-create-banking-info">
                        <x-form.section>
                            <x-form.row>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="payment_term">
                                    <option value="">Escolha um</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_15_DAYS }}">15 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_31_DAYS }}">31 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_45_DAYS }}">45 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_60_DAYS }}">60 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_75_DAYS }}">75 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_90_DAYS }}">90 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_120_DAYS }}">120 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_150_DAYS }}">150 dias</option>
                                    <option value="{{ \App\Models\Contract::PAYMENT_TERM_180_DAYS }}">180 dias</option>
                                </x-input.select>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="payment_day">
                                    <option value="" selected disabled>Escolha um dia</option>
                                    @for ($i = 1; $i <= 31; $i++)
                                        <option value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </x-input.select>
                                <x-input.select md="3" lg="3" :$resourceRoute :$action field="week_expiration_day">
                                    <option value="">Escolha um</option>
                                    <option value="{{ \Carbon\Carbon::SUNDAY }}">Domingo</option>
                                    <option value="{{ \Carbon\Carbon::MONDAY }}">Segunda-feira</option>
                                    <option value="{{ \Carbon\Carbon::TUESDAY }}">Terça-feira</option>
                                    <option value="{{ \Carbon\Carbon::WEDNESDAY }}">Quarta-feira</option>
                                    <option value="{{ \Carbon\Carbon::THURSDAY }}">Quinta-feira</option>
                                    <option value="{{ \Carbon\Carbon::FRIDAY }}">Sexta-feira</option>
                                    <option value="{{ \Carbon\Carbon::SATURDAY }}">Sábado</option>
                                </x-input.select>
                                <x-input.select md="3" lg="3" resourceRoute="providers" :$action field="preferred_earning_method">
                                    <option value="" disabled selected>Escolha um...</option>
                                    <option value="cash">Dinheiro</option>
                                    <option value="transfer">Transferência</option>
                                    <option value="pix">PIX</option>
                                </x-input.select>
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.select md="6" lg="6" resourceRoute="providers" action="show" field="bank_id" />
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="bank_code" disabled="true" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="branch_number" />
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="branch_digit" />
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="account_number" />
                                <x-input.text md="3" lg="3" resourceRoute="providers" action="show" field="account_digit" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text resourceRoute="providers" action="show" field="pix_1" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text resourceRoute="providers" action="show" field="pix_2" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.text resourceRoute="providers" action="show" field="pix_3" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.edit-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
    <script>
        $(document).ready(function() {
            const phoneMaskBehavior = function(val) {
                return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
            };

            const phoneOptions = {
                onKeyPress: function(val, e, field, options) {
                    field.mask(phoneMaskBehavior.apply({}, arguments), options);
                }
            };

            const taxIdNumberOptions = {
                onKeyPress: function(cpf, ev, el, op) {
                    var masks = ['000.000.000-000', '00.000.000/0000-00'];
                    $('.tax-id-number-input').mask((cpf.length > 14) ? masks[1] : masks[0], op);
                }
            };

            $('#exempt_from_state_registration').click(() => {
                if ($('#exempt_from_state_registration').is(':checked')) {
                    $('#state_registration_no').val('ISENTO');
                    $('#state_registration_no').prop('readonly', true);
                } else {
                    $('#state_registration_no').val(null);
                    $('#state_registration_no').removeAttr('readonly');
                }
            });

            $('#zipcode').mask('00000-000');
            $('#number').mask('99999');

            $('.phone-input').mask(phoneMaskBehavior, phoneOptions);

            $('.tax-id-number-input').mask('000.000.000-00', taxIdNumberOptions);

            $('#tax_id_number').blur(function() {
                $.ajax({
                    url: 'https://www.receitaws.com.br/v1/cnpj/' + this.value.match(/\d+/g).join(''),
                    crossDomain: true,
                    dataType: 'jsonp',
                    success: function(response) {
                        $('#name').val(response.nome);
                        $('#trading_name').val(response.fantasia);

                        $('#zipcode').val(response.cep.replace('.', ''));
                        $('#zipcode').trigger('blur');

                        $('#number').val(response.numero);
                        $('#additional_info').val(response.complemento);
                    }
                });
            });

            $('#zipcode').blur(function() {
                $.ajax({
                    url: `https://viacep.com.br/ws/${this.value}/json/`,
                    success: function(response) {
                        if (response.logradouro !== '') {
                            $('#address').prop('readonly', true);
                            $('#number').focus();
                        } else {
                            $('#address').prop('readonly', false);
                            $('#address').focus();
                        }

                        $('#additional_info').attr('readonly', response.complemento !== '');
                        $('#district').attr('readonly', response.bairro !== '');
                        $('#city').attr('readonly', response.localidade !== '');
                        $('#state').attr('readonly', response.uf !== '');
                        $('#additional_info').val(response.complemento);
                        $('#address').val(response.logradouro);
                        $('#district').val(response.bairro);
                        $('#city').val(response.localidade);
                        $('#state').val(response.uf);
                    }
                });
            });

            initializeSelect2(
                'bank_id',
                "{{ route('banks.get_by_code_or_name') }}",
                'Digite o código ou o nome de um banco',
                function(item) {
                    return {
                        id: item.id,
                        text: item.name,
                        code: item.code
                    };
                }
            );

            $('#bank_id').on('select2:select', function(e) {
                $('#bank_code').val(e.params.data.code);
            });
        });
    </script>
@endsection
