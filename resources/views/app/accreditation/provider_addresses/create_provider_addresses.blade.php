@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-12">
            @include('base.components.card.create.header', ['route' => route('providers.edit', $provider->id)])
            <div class="card shadow-sm">
                <div class="card-body">
                    <form class="form-group" method="post" action='{{ route("$resourceRoute.store", $provider->id) }}'>
                        <fieldset>
                            @csrf
                            @include('base.components.error_alert')

                            <div class="row">
                                <x-input.select md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="type" required="true">
                                    <option value="{{ \App\Models\ProviderAddress::TYPE_ATTENDANCE }}">Atendimento</option>
                                    <option value="{{ \App\Models\ProviderAddress::TYPE_BILLING }}">Faturamento</option>
                                    <option value="{{ \App\Models\ProviderAddress::TYPE_CHARGE }}">Cobrança</option>
                                </x-input.select>
                            </div>
                            <div class="row mt-3">
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="zipcode" />
                            </div>
                            <div class="row mt-3">
                                <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" action="create" field="address" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="number" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="additional_info" />
                            </div>
                            <div class="row mt-3">
                                <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" action="create" field="district" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="city" />
                                <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="create" field="state" />
                            </div>
                        </fieldset>

                        <x-button.save />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js-scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>
<script>
    $(document).ready(function() {
        const SPMaskBehavior = function(val) {
            return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
        };

        const spOptions = {
            onKeyPress: function(val, e, field, options) {
                field.mask(SPMaskBehavior.apply({}, arguments), options);
            }
        };

        $('#zipcode').mask('00000-000');
        $('#number').mask('99999');

        $('#zipcode').blur(function() {
            $.ajax({
                url: `https://viacep.com.br/ws/${this.value}/json/`,
                success: function(response) {
                    if (response.logradouro !== '') {
                        $('#address').prop('readonly', true);
                        $('#number').focus();
                    } else {
                        $('#address').prop('readonly', false);
                        $('#address').focus();
                    }

                    $('#additional_info').attr('readonly', response.complemento !== '');
                    $('#district').attr('readonly', response.bairro !== '');
                    $('#city').attr('readonly', response.localidade !== '');
                    $('#state').attr('readonly', response.uf !== '');
                    $('#additional_info').val(response.complemento);
                    $('#address').val(response.logradouro);
                    $('#district').val(response.bairro);
                    $('#city').val(response.localidade);
                    $('#state').val(response.uf);
                }
            });
        });
    });
</script>
@endsection
