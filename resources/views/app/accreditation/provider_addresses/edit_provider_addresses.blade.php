@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card-header d-flex align-items-center">
                    <strong class="card-header-title">{{ __("$resourceRoute.cards.edit.header.title") }}</strong>
                    <div class="ml-auto">
                        @include('base.components.buttons.back', ['route' => route('providers.edit', $providerAddress->provider_id)])
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-body">
                        <form method="post" class="form-group" action="{{ route('provider_addresses.update', [$providerAddress->provider_id, $providerAddress->id]) }}">
                            @csrf
                            @method('PUT')
                            @include('base.components.error_alert')

                            <fieldset>
                                <div class="row">
                                    <x-input.select md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="type" required="true">
                                        <option value="{{ \App\Models\ProviderAddress::TYPE_ATTENDANCE }}" @if ($providerAddress->type === \App\Models\ProviderAddress::TYPE_ATTENDANCE) selected @endif>Atendimento</option>
                                        <option value="{{ \App\Models\ProviderAddress::TYPE_BILLING }}" @if ($providerAddress->type === \App\Models\ProviderAddress::TYPE_BILLING) selected @endif>Faturamento</option>
                                        <option value="{{ \App\Models\ProviderAddress::TYPE_CHARGE }}" @if ($providerAddress->type === \App\Models\ProviderAddress::TYPE_CHARGE) selected @endif>Cobrança</option>
                                    </x-input.select>
                                </div>
                                <div class="row mt-3">
                                    <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="zipcode" value="{{ $providerAddress->zipcode }}" />
                                </div>
                                <div class="row mt-3">
                                    <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" action="edit" field="address" value="{{ $providerAddress->address }}" />
                                    <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="number" value="{{ $providerAddress->number }}" />
                                    <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="additional_info" value="{{ $providerAddress->additional_info }}" />
                                </div>
                                <div class="row mt-3">
                                    <x-input.text md="6" lg="6" :resourceRoute="$resourceRoute" action="edit" field="district" value="{{ $providerAddress->district }}" />
                                    <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="city" value="{{ $providerAddress->city }}" />
                                    <x-input.text md="3" lg="3" :resourceRoute="$resourceRoute" action="edit" field="state" value="{{ $providerAddress->state }}" />
                                </div>
                            </fieldset>

                            <x-button.save />
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
