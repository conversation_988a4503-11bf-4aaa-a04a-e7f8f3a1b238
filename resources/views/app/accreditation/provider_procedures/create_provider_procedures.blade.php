@extends('layouts.app')

@section('content')
    <x-action-card :$resourceRoute :$action :backRoute="route('providers.edit', request('provider'))">
        <form method="post" class="form-group" action="{{ route('provider_procedures.store', request('provider')) }}">
            @csrf
            @include('base.components.error_alert')

            <fieldset>
                <div class="row">
                    <x-input.select md="4" lg="4" :$resourceRoute :$action field="provider_id" required="true" disabled="true">
                        @foreach ($providers as $provider)
                            <option value="{{ $provider->id }}" @if ($provider->id === request('provider')->id) selected @endif>{{ $provider->name }}</option>
                        @endforeach
                    </x-input.select>
                    <x-input.select md="4" lg="4" :$resourceRoute :$action field="procedure_id" required="true" />
                    <x-input.text md="4" lg="4" :$resourceRoute :$action field="amount" required="true" />
                </div>
            </fieldset>

            <x-button.save />
        </form>
    </x-action-card>
@endsection

@section('js-scripts')
    <script src="{{ global_asset('js/core/components/mask-money/mask-money-helpers.js') }}"></script>
    <script>
        $(document).ready(function() {
            maskDefault();

            $('#procedure_id').select2({
                theme: 'bootstrap4',
                language: {
                    inputTooShort: function() {
                        return 'Digite um ou mais caracteres';
                    }
                },
                ajax: {
                    url: "{{ route('procedures.get_by_name') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term,
                            page: params.page,
                            from_erp_flex_only: true
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.map(function(item) {
                                return {
                                    id: item.id,
                                    text: item.name,
                                }
                            }),
                            pagination: {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                placeholder: "Digite o nome de um procedimento",
                minimumInputLength: 1,
            });
        });
    </script>
@endsection
