@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card-header d-flex align-items-center">
                    <strong class="card-header-title">{{ __("$resourceRoute.cards.show.header.title") }}</strong>
                    <div class="ml-auto">
                        @include('base.components.buttons.back', ['route' => $backRoute])
                    </div>
                </div>


                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="form-group">
                            <fieldset disabled>
                                <div class="row">
                                    <x-input.select md="4" lg="4" resourceRoute="provider_procedures" action="show" field="provider_id">
                                        @foreach ($providers as $provider)
                                            <option value="{{ $provider->id }}" @if ($provider->id === $providerProcedure->provider_id) selected @endif >{{ $provider->name }}</option>
                                        @endforeach
                                    </x-input.select>
                                    <x-input.select md="4" lg="4" resourceRoute="provider_procedures" action="show" field="procedure_id">
                                        @foreach ($procedures as $procedure)
                                            <option value="{{ $procedure->id }}" @if ($procedure->id === $providerProcedure->procedure_id) selected @endif >{{ $procedure->name }}</option>
                                        @endforeach
                                    </x-input.select>
                                    <x-input.text md="4" lg="4" resourceRoute="provider_procedures" action="show" field="amount" value="{{ $providerProcedure->friendly_amount }}" />
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
