@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card-header d-flex align-items-center">
                    <strong class="card-header-title">{{ __("$resourceRoute.cards.edit.header.title") }}</strong>
                    <div class="ml-auto">
                        @include('base.components.buttons.back', ['route' => route('providers.edit', $providerProcedure->provider_id)])
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-body">
                        <form method="post" class="form-group" action="{{ route('provider_procedures.update', [$providerProcedure->provider_id, $providerProcedure->procedure_id]) }}">
                            @csrf
                            @method('PUT')
                            @include('base.components.error_alert')

                            <fieldset>
                                <div class="row">
                                    <x-input.select md="4" lg="4" resourceRoute="provider_procedures" action="edit" field="provider_id" required="true" disabled="true">
                                        @foreach ($providers as $provider)
                                            <option value="{{ $provider->id }}" @if ($provider->id === $providerProcedure->provider_id) selected @endif>{{ $provider->name }}</option>
                                        @endforeach
                                    </x-input.select>
                                    <x-input.select md="4" lg="4" resourceRoute="provider_procedures" action="edit" field="procedure_id" required="true">
                                        <option value="{{ $providerProcedure->procedure_id }}">{{ $providerProcedure->procedure->name }}</option>
                                    </x-input.select>
                                    <x-input.text md="4" lg="4" resourceRoute="provider_procedures" action="edit" field="amount" required="true" value="{{ $providerProcedure->friendly_amount }}" />
                                </div>
                            </fieldset>

                            <x-button.save />
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js-scripts')
    <script src="{{ global_asset('js/core/components/mask-money/mask-money-helpers.js') }}"></script>
    <script>
        $(document).ready(function() {
            maskDefault();

            $('#procedure_id').select2({
                theme: 'bootstrap4',
                language: {
                    inputTooShort: function() {
                        return 'Digite um ou mais caracteres';
                    }
                },
                ajax: {
                    url: "{{ route('procedures.get_by_name') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term,
                            page: params.page,
                            from_erp_flex_only: true
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.map(function(item) {
                                return {
                                    id: item.id,
                                    text: item.name,
                                }
                            }),
                            pagination: {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                placeholder: "Digite o nome de um procedimento",
                minimumInputLength: 1,
            });
        });
    </script>
@endsection
