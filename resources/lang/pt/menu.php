<?php

use App\Core\Module;
use App\Models\BillingParameter;
use App\Models\ContractParameter;
use App\Models\EngineeringParameter;
use App\Models\FinanceParameter;
use App\Models\ManagementParameter;

return [

    Module::MANAGEMENT => [
        'title' => 'Gestão',
        'items' => [
            'general' => [
                'title' => 'Cadastros básicos',
                'items' => [
                    'roles' => [
                        'title' => 'Perfis',
                        'route' => 'roles.index'
                    ],
                    'operators' => [
                        'title' => 'Operadores',
                        'route' => 'operators.index'
                    ],
                    'holidays' => [
                        'title' => 'Feriados',
                        'route' => 'holidays.index'
                    ],
                    'indices' => [
                        'title' => 'Índices',
                        'route' => 'indices.index'
                    ],
                    'procedures' => [
                        'title' => 'Procedimentos',
                        'route' => 'procedures.index'
                    ],
                    'equivalences' => [
                        'title' => 'Equivalências',
                        'route' => 'procedures.equivalences'
                    ],
                    'expense_types' => [
                        'title' => 'Tipos de despesas',
                        'route' => 'expense_types.index'
                    ],
                    'contract_types' => [
                        'title' => 'Tipos de contrato',
                        'route' => 'contract_types.index'
                    ],
                    'cancellation_reasons' => [
                        'title' => 'Motivos de cancelamento',
                        'route' => 'cancellation_reasons.index'
                    ],
                    'companies' => [
                        'title' => 'Clientes',
                        'route' => 'companies.index'
                    ],
                    'suppliers' => [
                        'title' => 'Fornecedores',
                        'route' => 'suppliers.index'
                    ],
                    'providers' => [
                        'title' => 'Credenciados',
                        'route' => 'providers.index'
                    ],
                    'workflows' => [
                        'title' => 'Workflows',
                        'route' => 'workflows.index'
                    ],
                    'schedules' => [
                        'title' => 'Agenda',
                        'route' => 'schedules.index'
                    ],
                    'commission_rules' => [
                        'title' => 'Regras de comissão',
                        'route' => 'commission_rules.index'
                    ],
                    'cnaes' => [
                        'title' => 'CNAEs',
                        'route' => 'cnaes.index'
                    ],
                    'teams' => [
                        'title' => 'Departamentos',
                        'route' => 'teams.index'
                    ],
                    'soc_company_environments' => [
                        'title' => 'Empresas SOC',
                        'route' => 'soc_company_environments.index'
                    ],
                    'categories' => [
                        'title' => 'Categorias',
                        'route' => 'categories.index'
                    ],
                    'pre_companies' => [
                        'title' => 'Pré-cadastros (clientes)',
                        'route' => 'pre_companies.index'
                    ],
                    'checklists' => [
                        'title' => 'Checklists',
                        'route' => 'checklists.index'
                    ],
                    'company_groups' => [
                        'title' => 'Grupos empresariais',
                        'route' => 'company_groups.index',
                    ],
                    'reasons' => [
                        'title' => 'Motivos',
                        'route' => 'reasons.index',
                    ],
                ],
            ],
        ],
    ],

    Module::CONTRACT => [
        'title' => 'Contratos',
        'items' => [
            'general' => [
                'title' => 'Contratos',
                'items' => [
                    'contracts' => [
                        'title' => 'Contratos',
                        'route' => 'contracts.index'
                    ],
                    'contract_billing_groups' => [
                        'title' => 'Grupos de contratos',
                        'route' => 'contract_billing_groups.index'
                    ],
                    'contract_readjustments' => [
                        'title' => 'Reajuste',
                        'route' => 'contract_readjustments.index'
                    ],
                    'service_orders_renewal_panel' => [
                        'title' => 'Renovação de ordens de serviço',
                        'route' => 'service_orders.renewal_panel_index',
                    ],
                ],
            ],
        ],
    ],

    Module::ACCREDITATION => [
        'title' => 'Credenciamento',
        'items' => [
            'general' => [
                'title' => 'Credenciamento',
                'items' => [
                    'providers' => [
                        'title' => 'Credenciados',
                        'route' => 'providers.index'
                    ],
                    'provider_expenses' => [
                        'title' => 'Despesas',
                        'route' => 'provider_expenses.index'
                    ],
                    'provider_qualifications' => [
                        'title' => 'Qualificações',
                        'route' => 'provider_qualifications.index'
                    ],
                ],
            ],
        ],
    ],

    Module::PROVIDING => [
        'title' => 'Fornecimento',
        'items' => [
            'general' => [
                'title' => 'Fornecimento',
                'items' => [
                    'suppliers' => [
                        'title' => 'Fornecedores',
                        'route' => 'suppliers.index'
                    ],
                    'supplier_expenses' => [
                        'title' => 'Despesas',
                        'route' => 'supplier_expenses.index'
                    ],
                    'supplier_qualifications' => [
                        'title' => 'Qualificações',
                        'route' => 'supplier_qualifications.index'
                    ],
                ],
            ],
        ],
    ],

    Module::CRM => [
        'title' => 'CRM',
        'items' => [
            'general' => [
                'title' => 'CRM',
                'items' => [
                    'crm_city_coverages' => [
                        'title' => 'Abrangências',
                        'route' => 'crm_city_coverages.index',
                    ],
                    'crm_conversions' => [
                        'title' => 'Conversões',
                        'route' => 'crm_conversions.index',
                    ],
                    'lead_companies' => [
                        'title' => 'Empresas',
                        'route' => 'lead_companies.index',
                    ],
                    'crm_funnels' => [
                        'title' => 'Funis',
                        'route' => 'crm_funnels.index',
                    ],
                    'crm_indicators' => [
                        'title' => 'Indicadores',
                        'route' => 'crm.indicators',
                    ],
                    'crm_origins' => [
                        'title' => 'Origens',
                        'route' => 'crm_origins.index',
                    ],
                    'crm_funnels_panel' => [
                        'title' => 'Painel',
                        'route' => 'crm_funnels.panel',
                    ],
                    'crm_funnel_types' => [
                        'title' => 'Tipos de funil',
                        'route' => 'crm_funnel_types.index',
                    ],
                ],
            ],
        ],
    ],

    Module::AFTER_SALES => [
        'title' => 'Pós-vendas',
        'items' => [
            'general' => [
                'title' => 'Pós-vendas',
                'items' => [
                    'company_follow_ups_panel' => [
                        'title' => 'Painel de follow-ups',
                        'route' => 'company_follow_ups.panel',
                    ],
                    'company_follow_ups_indicators' => [
                        'title' => 'Indicadores',
                        'route' => 'company_follow_ups.indicators',
                    ],
                ],
            ],
        ],
    ],

    Module::OPERATION => [
        'title' => 'Operação',
        'items' => [
            'general' => [
                'title' => 'Operação',
                'items' => [
                    'exams' => [
                        'title' => 'Exames',
                        'route' => 'exams.index'
                    ],
                    'company_closing_glosses' => [
                        'title' => 'Glosas',
                        'route' => 'company_closing_glosses.index'
                    ]
                ]
            ]
        ],
    ],

    Module::ENGINEERING => [
        'title' => 'Engenharia',
        'items' => [
            'general' => [
                'title' => 'Engenharia',
                'items' => [
                    'service_orders_panel' => [
                        'title' => 'Ordens de serviço (painel)',
                        'route' => 'service_orders.panel'
                    ],
                    'service_orders_list' => [
                        'title' => 'Ordens de serviço (lista)',
                        'route' => 'service_orders.index',
                    ],
                ]
            ]
        ],
    ],

    Module::KNOWLEDGE_BASE => [
        'title' => 'Qualidade',
        'items' => [
            'general' => [
                'title' => 'Qualidade',
                'items' => [
                    'articles' => [
                        'title' => 'Artigos/procedimentos',
                        'route' => 'articles.index',
                    ],
                    'article_categories' => [
                        'title' => 'Categorias de artigos/procedimentos',
                        'route' => 'article_categories.index',
                    ],
                    'article_tags' => [
                        'title' => 'Tags de artigos/procedimentos',
                        'route' => 'article_tags.index',
                    ],
                ]
            ]
        ],
    ],

    Module::TICKETS => [
        'title' => 'Chamados',
        'items' => [
            'general' => [
                'title' => 'Chamados',
                'items' => [
                    'tickets' => [
                        'title' => 'Chamados',
                        'route' => 'tickets.index',
                    ],
                    'ticket_categories' => [
                        'title' => 'Categorias de chamados',
                        'route' => 'ticket_categories.index',
                    ],
                ]
            ]
        ],
    ],

    Module::BILLING => [
        'title' => 'Faturamento',
        'items' => [
            'general' => [
                'title' => 'Faturamento',
                'items' => [
                    'billing_entries' => [
                        'title' => 'Acréscimos/descontos',
                        'route' => 'billing_entries.index'
                    ],
                    'company_closing_groups' => [
                        'title' => 'Agrupamento de fechamentos',
                        'route' => 'company_closing_groups.index',
                    ],
                    'company_closings' => [
                        'title' => 'Faturamento (recorrência)',
                        'route' => 'company_closings.index'
                    ],
                    'stand_alone_contracts_for_billing' => [
                        'title' => 'Faturamento (parcelados)',
                        'route' => 'stand_alone_contracts_billing.index'
                    ],
                    'billing_panel' => [
                        'title' => 'Painel de faturamento',
                        'route' => 'billing_panel.index'
                    ]
                ]
            ]
        ],
    ],

    Module::FINANCE => [
        'title' => 'Financeiro',
        'items' => [
            'general' => [
                'title' => 'Financeiro',
                'items' => [
                    'receivables' => [
                        'title' => 'Contas a receber',
                        'route' => 'receivables.index'
                    ]
                ]
            ]
        ],
    ],

    Module::REPORTS => 'Relatórios',

    Module::IMPORTS => 'Importações',

    Module::PARAMETERS => [
        'title' => 'Parâmetros',
        'items' => [
            'general' => [
                'title' => 'Parâmetros',
                'items' => [
                    'management_parameters' => [
                        'title' => 'Gestão',
                        'route' => 'management_parameters.edit',
                        'parameters' => ManagementParameter::first()
                    ],
                    'contract_parameters' => [
                        'title' => 'Contratos',
                        'route' => 'contract_parameters.edit',
                        'parameters' => ContractParameter::first()
                    ],
                    'finance_parameters' => [
                        'title' => 'Financeiro',
                        'route' => 'finance_parameters.edit',
                        'parameters' => FinanceParameter::first()
                    ],
                    'billing_parameters' => [
                        'title' => 'Faturamento',
                        'route' => 'billing_parameters.edit',
                        'parameters' => BillingParameter::first()
                    ],
                    'engineering_parameters' => [
                        'title' => 'Engenharia',
                        'route' => 'engineering_parameters.edit',
                        'parameters' => EngineeringParameter::first(),
                    ],
                    'smtp_configurations' => [
                        'title' => 'Configurações SMTP',
                        'route' => 'smtp_configurations.index'
                    ]
                ]
            ]
        ],
    ],

    Module::AUDITING => [
        'title' => 'Auditoria',
        'items' => [
            'general' => [
                'title' => 'Auditoria',
                'items' => [
                    'divergences' => [
                        'title' => 'Despesas (credenciados)',
                        'route' => 'divergences.index',
                    ],
                    'missing_company_closing_exams' => [
                        'title' => 'Exames não faturados',
                        'route' => 'missing_company_closing_exams.index',
                    ],
                ]
            ]
        ],
    ],

    Module::LOGS => [
        'title' => 'Logs',
        'items' => [
            'general' => [
                'title' => 'Logs',
                'items' => [
                    'receivable_billing_emails' => [
                        'title' => 'E-mails (contas a receber)',
                        'route' => 'receivable_billing_emails.index',
                    ],
                    'erp_flex_integration_logs' => [
                        'title' => 'Integração ERPFlex',
                        'route' => 'erp_flex_integration_logs.index',
                    ],
                    'soc_integration_logs' => [
                        'title' => 'Integração SOC',
                        'route' => 'soc_integration_logs.index',
                    ],
                ]
            ]
        ],
    ],

    Module::INTEGRATIONS => [
        'title' => 'Integrações',
        'items' => [
            'general' => [
                'title' => 'Integrações',
                'items' => [
                    'erp_flex_integrations' => [
                        'title' => 'ERPFlex',
                        'route' => 'erp_flex_integrations.index'
                    ],
                    'soc_integrations' => [
                        'title' => 'SOC',
                        'route' => 'soc_integrations.index'
                    ],
                    'soc_integration_export_data_items' => [
                        'title' => 'SOC (exporta dados)',
                        'route' => 'soc_integration_export_data_items.index'
                    ]
                ]
            ]
        ],
    ],

    'change_password' => 'Alterar senha'

];
