html {
    height: 100%;
}

.base {
    height: 100%;

    &-container {
        height: 100%;

        &-navbar {
            background: white;
        }

        &-sidebar {
            height: 100vh;
            width: 28%;
            background-color: white;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            border-right: solid #e0e0e0 1px;
            min-width: 264px;
            overflow: auto;
            z-index: 1;

            &-upper {
                height: auto;
                padding-top: 5px;

                &-logo {
                    display: flex;
                    width: 100%;
                    justify-content: center;
                    height: 45%;
                }

                &-actions {
                    padding-top: 15px;
                    display: flex;
                    justify-content: center;
                    width: 100%;
                }
            }

            &-lower {
                height: auto;
                padding-left: 1rem;
                padding-right: 1rem;


                &-menu {
                    list-style: none;
                    padding: 0;

                    li {
                        list-style: none;
                        line-height: 2.58rem;
                        border-radius: 0.25rem;
                    }

                    &-module {
                        cursor: pointer;
                        padding-left: 0.7rem;
                        padding-right: 0.7rem;
                        line-height: 2.58rem;
                        border-radius: 0.25rem;
                        font-weight: bold;
                        color: #555555;

                        &:hover {
                            background: rgba(29, 92, 151, 1);
                            text-decoration: none;
                            color: #FFFFFF;

                            i {
                                color: #FFFFFF;
                            }
                        }
                    }
                }

                &-item {
                    padding-left: 0.7rem;
                    padding-right: 0.7rem;
                    line-height: 2.58rem;
                    border-radius: 0.25rem;

                    &-icon {
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        color: rgba(99, 129, 168, 0.7);
                    }

                    &-link {
                        color: #555555;
                    }

                    &-selected {
                        padding-left: 0.7rem;
                        padding-right: 0.7rem;
                        line-height: 2.58rem;
                        border-radius: 0.25rem;
                        background: rgba(29, 92, 151, 1);
                        text-decoration: none;

                        i, span {
                            color: #FFFFFF;
                        }
                    }

                    &:hover {
                        background: rgba(29, 92, 151, 1);
                        text-decoration: none;

                        i, span {
                            color: #FFFFFF;
                        }
                    }
                }

            }

            &-footer {
                padding-left: 1rem;
                padding-right: 1rem;


                &-menu {
                    list-style: none;
                    padding: 0;

                    li {
                        list-style: none;
                        line-height: 2.58rem;
                        border-radius: 0.25rem;
                    }
                }

                &-item {
                    padding-left: 0.7rem;
                    padding-right: 0.7rem;
                    line-height: 2.58rem;
                    border-radius: 0.25rem;

                    &-link {
                        font-weight: bold;
                        color: rgba(99, 129, 168, 0.58);
                    }

                    &:hover {
                        background: rgba(29, 92, 151, 1);
                        text-decoration: none;

                        i, span {
                            color: #FFFFFF;
                        }
                    }
                }

            }
        }

        &-content {
            width: 100%;
        }

        &-aside {
            height: 100vh;
            width: 15%;
            background-color: white;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            border-left: solid #e0e0e0 1px;
            min-width: 150px;
            overflow: auto;
            z-index: 1;

            &-upper {
                height: auto;
                padding-top: 5px;

                &-logo {
                    display: flex;
                    width: 100%;
                    justify-content: center;
                    height: 45%;
                }

                &-actions {
                    padding-top: 15px;
                    display: flex;
                    justify-content: center;
                    width: 100%;
                }
            }

            &-lower {
                height: auto;
                padding-left: 1rem;
                padding-right: 1rem;


                &-menu {
                    list-style: none;
                    padding: 0;

                    li {
                        list-style: none;
                        line-height: 2.58rem;
                        border-radius: 0.25rem;
                    }

                    &-module {
                        cursor: pointer;
                        padding-left: 0.7rem;
                        padding-right: 0.7rem;
                        line-height: 2.58rem;
                        border-radius: 0.25rem;
                        font-weight: bold;
                        color: #555555;

                        &:hover {
                            background: rgba(29, 92, 151, 1);
                            text-decoration: none;
                            color: #FFFFFF;

                            i {
                                color: #FFFFFF;
                            }
                        }
                    }
                }

                &-item {
                    padding-left: 0.7rem;
                    padding-right: 0.7rem;
                    line-height: 2.58rem;
                    border-radius: 0.25rem;

                    &-icon {
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        color: rgba(99, 129, 168, 0.34);
                    }

                    &-link {
                        font-weight: bold;
                        color: #555555;
                    }

                    &-selected {
                        padding-left: 0.7rem;
                        padding-right: 0.7rem;
                        line-height: 2.58rem;
                        border-radius: 0.25rem;
                        background: rgba(29, 92, 151, 1);
                        text-decoration: none;

                        i, span {
                            color: #FFFFFF;
                        }
                    }

                    &:hover {
                        background: rgba(29, 92, 151, 1);
                        text-decoration: none;

                        i, span {
                            color: #FFFFFF;
                        }
                    }
                }

            }

            &-footer {
                padding-left: 1rem;
                padding-right: 1rem;


                &-menu {
                    list-style: none;
                    padding: 0;

                    li {
                        list-style: none;
                        line-height: 2.58rem;
                        border-radius: 0.25rem;
                    }
                }

                &-item {
                    padding-left: 0.7rem;
                    padding-right: 0.7rem;
                    line-height: 2.58rem;
                    border-radius: 0.25rem;

                    &-link {
                        font-weight: bold;
                        color: rgba(99, 129, 168, 0.58);
                    }

                    &:hover {
                        background: rgba(29, 92, 151, 1);
                        text-decoration: none;

                        i, span {
                            color: #FFFFFF;
                        }
                    }
                }

            }
        }
    }
}

.dropdown-content {
    display: none;
}

.show {
    display: block;
}
