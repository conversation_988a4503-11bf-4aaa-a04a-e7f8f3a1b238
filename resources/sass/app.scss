// Fonts
@import url('https://fonts.googleapis.com/css?family=Nunito+Sans:400,700');

// Variables
@import 'variables';

// Bootstrap
@import '~bootstrap/scss/bootstrap';

// Application stylesheets
@import "general/_base";
@import "general/_card";
@import "general/_livewire";
@import "general/_check";
@import "beneficiary/_group-members";

.ctm {
    &-img-nav {
        max-height: 110px;
    }

    &-menu-img-nav {
        max-height: 50px;
    }

    &-button {
        &-box {
            width: 250px;
            padding: 20px;
        }
    }
}

.page-header {
    display: flex;
    justify-content: space-between;
    padding-bottom: 1.5rem;
}

@media (max-width: 767px) {
    .page-header {
        display: block;
    }
}

button:disabled {
    cursor: not-allowed;
    pointer-events: all !important;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

/* Hide default HTML checkbox */
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* The slider */
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked+.slider {
    background-color: #2196F3;
}

input:focus+.slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked+.slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.btn-content {
    height: 37px;
}

.reason-modal {
    cursor: pointer;
}


/* Os cards kanban */
.expired,
.rework {
    background-color: #c1201a;
    color: #fff;

    &:empty {
        display: inline-block;
    }
}

.expiring {
    background-color: #ffed4a;
    color: #212529;

    &:empty {
        display: inline-block;
    }
}

.on-time {
    background-color: #1d5c97;
    color: #fff;

    &:empty {
        display: inline-block;
    }
}

.badge {
    &-expired {
        @extend .expired;

        &:after {
            content: "Vencido";
        }
    }

    &-rework {
        @extend .rework;

        &:after {
            content: "Retrabalho";
        }
    }

    &-expiring {
        @extend .expiring;

        &:after {
            content: "Vencendo";
        }
    }

    &-on-time {
        @extend .on-time;

        &:after {
            content: "No Prazo";
        }
    }
}

.ml-10 {
    margin-left: 10px;
}

.scroll-x {
    width: auto;
    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;
}
