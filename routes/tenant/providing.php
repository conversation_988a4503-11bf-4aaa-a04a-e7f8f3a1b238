<?php

use App\Actions\Supplier\ActivateSupplier;
use App\Actions\Supplier\CreateSupplier;
use App\Actions\Supplier\DeleteSupplier;
use App\Actions\Supplier\EditSupplier;
use App\Actions\Supplier\GetSupplier;
use App\Actions\Supplier\GetSuppliers;
use App\Actions\Supplier\GetSuppliersByNameTradingNameOrTaxIdNumber;
use App\Actions\Supplier\GetSuppliersFromErpFlex;
use App\Actions\Supplier\ImportSuppliers;
use App\Actions\Supplier\InactivateSupplier;
use App\Actions\Supplier\SendSupplierToErpFlex;
use App\Actions\SupplierExpenseType\CreateSupplierExpenseType;
use App\Actions\SupplierExpenseType\DeleteSupplierExpenseType;
use App\Actions\SupplierExpenseType\EditSupplierExpenseType;
use App\Actions\SupplierExpenseType\GetSupplierExpenseType;
use App\Actions\SupplierExpenseType\GetSupplierExpenseTypes;
use App\Actions\SupplierExpenseType\GetSupplierExpenseTypesBySupplierIdForSelect;
use App\Actions\SupplierExpense\ApproveSupplierExpense;
use App\Actions\SupplierExpense\CreateSupplierExpense;
use App\Actions\SupplierExpense\DeleteSupplierExpense;
use App\Actions\SupplierExpense\EditSupplierExpense;
use App\Actions\SupplierExpense\GetSupplierExpense;
use App\Actions\SupplierExpense\GetSupplierExpenses;
use App\Actions\SupplierExpense\RejectSupplierExpense;
use App\Actions\SupplierExpenseFile\DownloadAllSupplierExpenseFiles;
use App\Actions\SupplierExpenseFile\DownloadSupplierExpenseFile;
use App\Actions\SupplierExpenseFile\GetSupplierExpenseFiles;
use App\Actions\ServiceOrder\GetServiceOrdersForSelect;
use App\Actions\SupplierQualification\CreateSupplierQualification;
use App\Actions\SupplierQualification\DeleteSupplierQualification;
use App\Actions\SupplierQualification\DeleteSupplierQualifications;
use App\Actions\SupplierQualification\GetSupplierQualification;
use App\Actions\SupplierQualification\GetSupplierQualifications;
use App\Actions\SupplierQualificationCriteria\CreateSupplierQualificationCriterion;
use App\Actions\SupplierQualificationCriteria\DeleteSupplierQualificationCriteria;
use App\Actions\SupplierQualificationCriteria\DeleteSupplierQualificationCriterion;
use App\Actions\SupplierQualificationCriteria\EditSupplierQualificationCriterion;
use App\Actions\SupplierQualificationCriteria\GetSupplierQualificationCriteria;
use App\Actions\SupplierQualificationCriteria\GetSupplierQualificationCriterion;

Route::get('service-orders/get-for-select', GetServiceOrdersForSelect::class)->name('service_orders.get_for_select');

Route::prefix('suppliers')->group(function () {
    Route::get('', GetSuppliers::class)->name('suppliers.index');
    Route::get('get-for-datatable', GetSuppliers::class)->name('suppliers.get_for_datatable');
    Route::get('get-by-name-trading-name-or-tax-id-number', GetSuppliersByNameTradingNameOrTaxIdNumber::class)->name('suppliers.get_by_name_trading_name_or_tax_id_number');
    Route::post('get-from-erp-flex', GetSuppliersFromErpFlex::class)->name('suppliers.get_from_erp_flex');
    Route::get('create', CreateSupplier::class)->name('suppliers.create');
    Route::post('', CreateSupplier::class)->name('suppliers.store');
    Route::post('import', ImportSuppliers::class)->name('suppliers.import');
    Route::prefix('{supplier}')->group(function () {
        Route::get('', GetSupplier::class)->name('suppliers.show');
        Route::get('edit', EditSupplier::class)->name('suppliers.edit');
        Route::post('activate', ActivateSupplier::class)->name('suppliers.activate');
        Route::post('inactivate', InactivateSupplier::class)->name('suppliers.inactivate');
        Route::post('send-to-erp-flex', SendSupplierToErpFlex::class)->name('suppliers.send_to_erp_flex');
        Route::put('', EditSupplier::class)->name('suppliers.update');
        Route::delete('', DeleteSupplier::class)->name('suppliers.destroy');
        Route::prefix('expense-types')->group(function () {
            Route::get('get-for-datatable', GetSupplierExpenseTypes::class)->name('supplier_expense_types.get_for_datatable');
            Route::get('get-by-supplier-for-select', GetSupplierExpenseTypesBySupplierIdForSelect::class)->name('supplier_expense_types.get_by_supplier_for_select');
            Route::get('create', CreateSupplierExpenseType::class)->name('supplier_expense_types.create');
            Route::post('', CreateSupplierExpenseType::class)->name('supplier_expense_types.store');
            Route::prefix('{expense_type}')->group(function () {
                Route::get('', GetSupplierExpenseType::class)->name('supplier_expense_types.show');
                Route::get('edit', EditSupplierExpenseType::class)->name('supplier_expense_types.edit');
                Route::put('', EditSupplierExpenseType::class)->name('supplier_expense_types.update');
                Route::delete('', DeleteSupplierExpenseType::class)->name('supplier_expense_types.destroy');
            });
        });
    });
});

Route::prefix('supplier-expenses')->group(function () {
    Route::get('', GetSupplierExpenses::class)->name('supplier_expenses.index');
    Route::get('create', CreateSupplierExpense::class)->name('supplier_expenses.create');
    Route::post('', CreateSupplierExpense::class)->name('supplier_expenses.store');
    Route::prefix('{supplier_expense}')->group(function () {
        Route::get('', GetSupplierExpense::class)->name('supplier_expenses.show');
        Route::get('edit', EditSupplierExpense::class)->name('supplier_expenses.edit');
        Route::post('approve', ApproveSupplierExpense::class)->name('supplier_expenses.approve');
        Route::post('reject', RejectSupplierExpense::class)->name('supplier_expenses.reject');
        Route::put('', EditSupplierExpense::class)->name('supplier_expenses.update');
        Route::delete('', DeleteSupplierExpense::class)->name('supplier_expenses.destroy');
        Route::prefix('files')->group(function () {
            Route::get('', GetSupplierExpenseFiles::class)->name('supplier_expense_files.index');
            Route::get('download', DownloadAllSupplierExpenseFiles::class)->name('supplier_expense_files.download_all');
            Route::prefix('{supplier_expense_file}')->group(function () {
                Route::get('download', DownloadSupplierExpenseFile::class)->name('supplier_expense_files.download');
            });
        });
    });
});

Route::prefix('supplier-qualifications')->group(function () {
    Route::get('', GetSupplierQualifications::class)->name('supplier_qualifications.index');
    Route::get('create', CreateSupplierQualification::class)->name('supplier_qualifications.create');
    Route::post('', CreateSupplierQualification::class)->name('supplier_qualifications.store');
    Route::delete('delete-batch', DeleteSupplierQualifications::class)->name('supplier_qualifications.delete_batch');
    Route::prefix('{supplier_qualification}')->group(function () {
        Route::get('', GetSupplierQualification::class)->name('supplier_qualifications.show');
        Route::delete('', DeleteSupplierQualification::class)->name('supplier_qualifications.destroy');
    });
});

Route::prefix('supplier-qualification-criteria')->group(function () {
    Route::get('', GetSupplierQualificationCriteria::class)->name('supplier_qualification_criteria.index');
    Route::get('create', CreateSupplierQualificationCriterion::class)->name('supplier_qualification_criteria.create');
    Route::post('', CreateSupplierQualificationCriterion::class)->name('supplier_qualification_criteria.store');
    Route::delete('delete-batch', DeleteSupplierQualificationCriteria::class)->name('supplier_qualification_criteria.delete_batch');
    Route::prefix('{supplier_qualification_criterion}')->group(function () {
        Route::get('', GetSupplierQualificationCriterion::class)->name('supplier_qualification_criteria.show');
        Route::get('edit', EditSupplierQualificationCriterion::class)->name('supplier_qualification_criteria.edit');
        Route::put('', EditSupplierQualificationCriterion::class)->name('supplier_qualification_criteria.update');
        Route::delete('', DeleteSupplierQualificationCriterion::class)->name('supplier_qualification_criteria.destroy');
    });
});
