<?php

use App\Actions\Company\CreateCompanyFromLeadCompany;
use App\Actions\CrmCityCoverage\CreateCrmCityCoverage;
use App\Actions\CrmCityCoverage\DeleteCrmCityCoverages;
use App\Actions\CrmCityCoverage\EditCrmCityCoverage;
use App\Actions\CrmCityCoverage\GetCrmCityCoverage;
use App\Actions\CrmCityCoverage\GetCrmCityCoverages;
use App\Actions\CrmCityCoverageCity\CreateCrmCityCoverageCity;
use App\Actions\CrmCityCoverageCity\DeleteCrmCityCoverageCities;
use App\Actions\CrmCityCoverageCity\EditCrmCityCoverageCity;
use App\Actions\CrmCityCoverageCity\GetCrmCityCoverageCity;
use App\Actions\CrmConversion\CreateLeadCompanyFromCrmConversion;
use App\Actions\CrmConversion\DeleteCrmConversions;
use App\Actions\CrmConversion\GetCrmConversion;
use App\Actions\CrmConversion\GetCrmConversions;
use App\Actions\CrmFunnel\CreateCrmFunnel;
use App\Actions\CrmFunnel\DeleteCrmFunnels;
use App\Actions\CrmFunnel\EditCrmFunnel;
use App\Actions\CrmFunnel\GetCrmFunnel;
use App\Actions\CrmFunnel\GetCrmFunnels;
use App\Actions\CrmFunnel\GetCrmFunnelsPanel;
use App\Actions\CrmFunnel\SetCrmFunnelSessionDateFilters;
use App\Actions\CrmFunnelOperator\CreateCrmFunnelOperator;
use App\Actions\CrmFunnelOperator\DeleteCrmFunnelOperators;
use App\Actions\CrmFunnelOperator\EditCrmFunnelOperator;
use App\Actions\CrmFunnelOperator\GetCrmFunnelOperator;
use App\Actions\CrmFunnelStep\CreateCrmFunnelStep;
use App\Actions\CrmFunnelStep\DeleteCrmFunnelSteps;
use App\Actions\CrmFunnelStep\EditCrmFunnelStep;
use App\Actions\CrmFunnelStep\GetCrmFunnelStep;
use App\Actions\CrmFunnelType\CreateCrmFunnelType;
use App\Actions\CrmFunnelType\DeleteCrmFunnelTypes;
use App\Actions\CrmFunnelType\EditCrmFunnelType;
use App\Actions\CrmFunnelType\GetCrmFunnelType;
use App\Actions\CrmFunnelType\GetCrmFunnelTypes;
use App\Actions\CrmIndicators\GetCrmIndicators;
use App\Actions\CrmOrigin\CreateCrmOrigin;
use App\Actions\CrmOrigin\DeleteCrmOrigins;
use App\Actions\CrmOrigin\EditCrmOrigin;
use App\Actions\CrmOrigin\GetCrmOrigin;
use App\Actions\CrmOrigin\GetCrmOrigins;
use App\Actions\Lead\CloneLead;
use App\Actions\Lead\CreateLead;
use App\Actions\Lead\CreateLeadFromLeadCompany;
use App\Actions\Lead\DeleteLead;
use App\Actions\Lead\EditLead;
use App\Actions\Lead\GetLead;
use App\Actions\Lead\GetLeads;
use App\Actions\Lead\RegisterLeadDeal;
use App\Actions\Lead\RegisterLeadLoss;
use App\Actions\Lead\UndoLeadDeal;
use App\Actions\Lead\UndoLeadLoss;
use App\Actions\Lead\UpdateLeadCrmFunnel;
use App\Actions\Lead\UpdateLeadCrmFunnelStep;
use App\Actions\LeadAnnotation\CreateLeadAnnotation;
use App\Actions\LeadCompany\CreateLeadCompany;
use App\Actions\LeadCompany\DeleteLeadCompanies;
use App\Actions\LeadCompany\EditLeadCompany;
use App\Actions\LeadCompany\GetLeadCompanies;
use App\Actions\LeadCompany\GetLeadCompaniesByNameTradingNameOrTaxIdentificationNumber;
use App\Actions\LeadCompany\GetLeadCompany;
use App\Actions\LeadCompanyContact\CreateLeadCompanyContact;
use App\Actions\LeadCompanyContact\DeleteLeadCompanyContacts;
use App\Actions\LeadCompanyContact\EditLeadCompanyContact;
use App\Actions\LeadCompanyContact\GetLeadCompanyContact;
use App\Actions\LeadProposal\CreateLeadProposal;
use App\Actions\LeadProposal\GenerateLeadProposal;
use App\Actions\LeadProposal\SendLeadProposalEmail;
use App\Actions\LeadProposalContract\CreateLeadProposalContract;
use App\Actions\LeadProposalContract\GenerateLeadProposalContract;
use App\Actions\LeadProposalContract\Integrations\ZapSign\CreateLeadProposalContractInZapSign;

Route::prefix('crm-conversion')->group(function () {
    Route::get('', GetCrmConversions::class)->name('crm_conversions.index');
    Route::delete('delete-batch', DeleteCrmConversions::class)->name('crm_conversions.delete_batch');
    Route::prefix('{crm_conversion}')->group(function () {
        Route::get('', GetCrmConversion::class)->name('crm_conversions.show');
        Route::post('create-lead-company', CreateLeadCompanyFromCrmConversion::class)->name('crm_conversions.create_lead_company');
    });
});

Route::prefix('crm-origin')->group(function () {
    Route::get('', GetCrmOrigins::class)->name('crm_origins.index');
    Route::get('create', CreateCrmOrigin::class)->name('crm_origins.create');
    Route::post('', CreateCrmOrigin::class)->name('crm_origins.store');
    Route::delete('delete-batch', DeleteCrmOrigins::class)->name('crm_origins.delete_batch');
    Route::prefix('{crm_origin}')->group(function () {
        Route::get('', GetCrmOrigin::class)->name('crm_origins.show');
        Route::get('edit', EditCrmOrigin::class)->name('crm_origins.edit');
        Route::put('', EditCrmOrigin::class)->name('crm_origins.update');
    });
});

Route::prefix('crm-city-coverages')->group(function () {
    Route::get('', GetCrmCityCoverages::class)->name('crm_city_coverages.index');
    Route::get('create', CreateCrmCityCoverage::class)->name('crm_city_coverages.create');
    Route::post('', CreateCrmCityCoverage::class)->name('crm_city_coverages.store');
    Route::delete('delete-batch', DeleteCrmCityCoverages::class)->name('crm_city_coverages.delete_batch');
    Route::prefix('{crm_city_coverage}')->group(function () {
        Route::get('', GetCrmCityCoverage::class)->name('crm_city_coverages.show');
        Route::get('edit', EditCrmCityCoverage::class)->name('crm_city_coverages.edit');
        Route::put('', EditCrmCityCoverage::class)->name('crm_city_coverages.update');
        Route::prefix('cities')->group(function () {
            Route::get('create', CreateCrmCityCoverageCity::class)->name('crm_city_coverage_cities.create');
            Route::post('', CreateCrmCityCoverageCity::class)->name('crm_city_coverage_cities.store');
            Route::delete('delete-batch', DeleteCrmCityCoverageCities::class)->name('crm_city_coverage_cities.delete_batch');
            Route::prefix('{crm_city_coverage_city}')->group(function () {
                Route::get('', GetCrmCityCoverageCity::class)->name('crm_city_coverage_cities.show');
                Route::get('edit', EditCrmCityCoverageCity::class)->name('crm_city_coverage_cities.edit');
                Route::put('', EditCrmCityCoverageCity::class)->name('crm_city_coverage_cities.update');
            });
        });
    });
});

Route::prefix('lead-companies')->group(function () {
    Route::get('', GetLeadCompanies::class)->name('lead_companies.index');
    Route::get('get-by-name-trading-name-or-tax-id-number', GetLeadCompaniesByNameTradingNameOrTaxIdentificationNumber::class)->name('lead_companies.get_by_name_trading_name_or_tax_id_number');
    Route::get('create', CreateLeadCompany::class)->name('lead_companies.create');
    Route::post('', CreateLeadCompany::class)->name('lead_companies.store');
    Route::delete('delete-batch', DeleteLeadCompanies::class)->name('lead_companies.delete_batch');
    Route::prefix('{lead_company}')->group(function () {
        Route::get('create-lead', CreateLeadFromLeadCompany::class)->name('lead_companies.create_lead');
        Route::get('', GetLeadCompany::class)->name('lead_companies.show');
        Route::get('edit', EditLeadCompany::class)->name('lead_companies.edit');
        Route::get('create-from-lead-company', CreateCompanyFromLeadCompany::class)->name('lead_companies.create_from_lead_company');
        Route::put('', EditLeadCompany::class)->name('lead_companies.update');
        Route::prefix('contacts')->group(function () {
            Route::get('create', CreateLeadCompanyContact::class)->name('lead_company_contacts.create');
            Route::post('', CreateLeadCompanyContact::class)->name('lead_company_contacts.store');
            Route::delete('delete-batch', DeleteLeadCompanyContacts::class)->name('lead_company_contacts.delete_batch');
            Route::prefix('{lead_company_contact}')->group(function () {
                Route::get('', GetLeadCompanyContact::class)->name('lead_company_contacts.show');
                Route::get('edit', EditLeadCompanyContact::class)->name('lead_company_contacts.edit');
                Route::put('', EditLeadCompanyContact::class)->name('lead_company_contacts.update');
            });
        });
    });
});

Route::prefix('crm-funnels')->group(function () {
    Route::get('', GetCrmFunnels::class)->name('crm_funnels.index');
    Route::get('panel', GetCrmFunnelsPanel::class)->name('crm_funnels.panel');
    Route::post('panel', GetCrmFunnelsPanel::class)->name('crm_funnels.panel');
    Route::post('set-session-date-filters', SetCrmFunnelSessionDateFilters::class)->name('crm_funnels.set_session_date_filters');
    Route::get('create', CreateCrmFunnel::class)->name('crm_funnels.create');
    Route::post('', CreateCrmFunnel::class)->name('crm_funnels.store');
    Route::delete('delete-batch', DeleteCrmFunnels::class)->name('crm_funnels.delete_batch');
    Route::prefix('{crm_funnel}')->group(function () {
        Route::get('', GetCrmFunnel::class)->name('crm_funnels.show');
        Route::get('edit', EditCrmFunnel::class)->name('crm_funnels.edit');
        Route::put('', EditCrmFunnel::class)->name('crm_funnels.update');
        Route::prefix('steps')->group(function () {
            Route::get('create', CreateCrmFunnelStep::class)->name('crm_funnel_steps.create');
            Route::post('', CreateCrmFunnelStep::class)->name('crm_funnel_steps.store');
            Route::delete('delete-batch', DeleteCrmFunnelSteps::class)->name('crm_funnel_steps.delete_batch');
            Route::prefix('{crm_funnel_step}')->group(function () {
                Route::get('', GetCrmFunnelStep::class)->name('crm_funnel_steps.show');
                Route::get('edit', EditCrmFunnelStep::class)->name('crm_funnel_steps.edit');
                Route::put('', EditCrmFunnelStep::class)->name('crm_funnel_steps.update');
            });
        });
        Route::prefix('operators')->group(function () {
            Route::get('create', CreateCrmFunnelOperator::class)->name('crm_funnel_operators.create');
            Route::post('', CreateCrmFunnelOperator::class)->name('crm_funnel_operators.store');
            Route::delete('delete-batch', DeleteCrmFunnelOperators::class)->name('crm_funnel_operators.delete_batch');
            Route::prefix('{crm_funnel_operator}')->group(function () {
                Route::get('', GetCrmFunnelOperator::class)->name('crm_funnel_operators.show');
                Route::get('edit', EditCrmFunnelOperator::class)->name('crm_funnel_operators.edit');
                Route::put('', EditCrmFunnelOperator::class)->name('crm_funnel_operators.update');
            });
        });
    });
});

Route::prefix('leads')->group(function () {
    Route::get('', GetLeads::class)->name('leads.index');
    Route::get('create', CreateLead::class)->name('leads.create');
    Route::post('', CreateLead::class)->name('leads.store');
    // Route::delete('delete-batch', DeleteLeads::class)->name('leads.delete_batch');
    Route::post('update-lead-crm-funnel-step', UpdateLeadCrmFunnelStep::class)->name('leads.update_lead_crm_funnel_step');
    Route::prefix('{lead}')->group(function () {
        Route::get('', GetLead::class)->name('leads.show');
        Route::get('edit', EditLead::class)->name('leads.edit');
        Route::put('', EditLead::class)->name('leads.update');
        Route::post('register-deal', RegisterLeadDeal::class)->name('leads.register_deal');
        Route::post('undo-deal', UndoLeadDeal::class)->name('leads.undo_deal');
        Route::post('register-loss', RegisterLeadLoss::class)->name('leads.register_loss');
        Route::post('undo-loss', UndoLeadLoss::class)->name('leads.undo_loss');
        Route::post('update-crm-funnel', UpdateLeadCrmFunnel::class)->name('leads.update_crm_funnel');
        Route::post('clone', CloneLead::class)->name('leads.clone');
        Route::delete('', DeleteLead::class)->name('leads.destroy');
        Route::prefix('proposals')->group(function () {
            Route::get('create', CreateLeadProposal::class)->name('lead_proposals.create');
            Route::post('', CreateLeadProposal::class)->name('lead_proposals.store');
            Route::prefix('{lead_proposal}')->group(function () {
                Route::get('send-email', SendLeadProposalEmail::class)->name('lead_proposals.send_email');
                Route::post('send-email', SendLeadProposalEmail::class)->name('lead_proposals.send_email');
                Route::post('pdf', GenerateLeadProposal::class)->name('lead_proposals.pdf');
                Route::prefix('contracts')->group(function () {
                    Route::post('', CreateLeadProposalContract::class)->name('lead_proposal_contracts.store');
                    Route::prefix('{lead_proposal_contract}')->group(function () {
                        Route::get('pdf', GenerateLeadProposalContract::class)->name('lead_proposal_contracts.pdf');
                        Route::get('create-in-zap-sign', CreateLeadProposalContractInZapSign::class)->name('lead_proposal_contracts.create_in_zap_sign');
                    });
                });
            });
        });
        Route::prefix('annotations')->group(function () {
            Route::post('', CreateLeadAnnotation::class)->name('lead_annotations.store');
        });
    });
});

Route::prefix('crm-funnel-types')->group(function () {
    Route::get('', GetCrmFunnelTypes::class)->name('crm_funnel_types.index');
    Route::get('create', CreateCrmFunnelType::class)->name('crm_funnel_types.create');
    Route::post('', CreateCrmFunnelType::class)->name('crm_funnel_types.store');
    Route::delete('delete-batch', DeleteCrmFunnelTypes::class)->name('crm_funnel_types.delete_batch');
    Route::prefix('{crm_funnel_type}')->group(function () {
        Route::get('', GetCrmFunnelType::class)->name('crm_funnel_types.show');
        Route::get('edit', EditCrmFunnelType::class)->name('crm_funnel_types.edit');
        Route::put('', EditCrmFunnelType::class)->name('crm_funnel_types.update');
    });
});

Route::get('indicators', GetCrmIndicators::class)->name('crm.indicators');
Route::post('indicators', GetCrmIndicators::class)->name('crm.indicators');
