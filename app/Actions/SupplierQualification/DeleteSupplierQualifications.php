<?php

namespace App\Actions\SupplierQualification;

use App\Core\Http\Requests\DeleteBatchRequest;
use App\Models\Permission;
use App\Models\SupplierQualification;
use Illuminate\Http\RedirectResponse;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteSupplierQualifications
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::DELETE_SUPPLIER_QUALIFICATIONS);
    }

    public function asController(DeleteBatchRequest $request): RedirectResponse
    {
        try {
            $this->handle($request->getDeleteIdsArray());
            return delete_batch_redirect_success('supplier_qualifications.index');
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $ids)
    {
        try {
            collect($ids)->each(function (string $id): void {
                DeleteSupplierQualification::run(SupplierQualification::find($id));
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
