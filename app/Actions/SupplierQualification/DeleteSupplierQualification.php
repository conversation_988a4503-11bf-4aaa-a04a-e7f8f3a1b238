<?php

namespace App\Actions\SupplierQualification;

use App\Models\SupplierQualification;
use App\Models\SupplierQualificationLog;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteSupplierQualification
{
    use AsAction;

    public function handle(SupplierQualification $supplierQualification)
    {
        try {
            DB::transaction(function () use ($supplierQualification) {
                SupplierQualificationLog::create([
                    'supplier_qualification_id' => $supplierQualification->id,
                    'operator_id' => auth()->id(),
                    'action' => SupplierQualificationLog::ACTION_DELETED,
                    'data' => [
                        'supplier_id' => $supplierQualification->supplier_id,
                        'grade' => $supplierQualification->grade,
                        'qualified_at' => $supplierQualification->qualified_at,
                        'qualification_expires_at' => $supplierQualification->qualification_expires_at,
                        'deleted_at' => now(),
                    ],
                ]);

                $supplierQualification->supplierQualificationItems()->delete();
                $supplierQualification->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
