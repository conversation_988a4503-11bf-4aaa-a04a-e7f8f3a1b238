<?php

namespace App\Actions\SupplierQualification;

use App\Models\Permission;
use App\Models\SupplierQualification;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetSupplierQualifications
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_SUPPLIER_QUALIFICATIONS);
    }

    public function handle(): Factory|View
    {
        return SupplierQualification::getBackEndActionGenerator()->loadIndexView();
    }
}
