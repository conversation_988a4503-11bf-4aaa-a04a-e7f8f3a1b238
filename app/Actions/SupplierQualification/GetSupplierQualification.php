<?php

namespace App\Actions\SupplierQualification;

use App\Models\Permission;
use App\Models\SupplierQualification;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GetSupplierQualification
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_SUPPLIER_QUALIFICATIONS);
    }

    public function handle(SupplierQualification $supplierQualification): Factory|View
    {
        try {
            // Load the qualification with all related data
            $supplierQualification->load([
                'supplier',
                'supplierQualificationItems.supplierQualificationCriterion'
            ]);

            return $supplierQualification->getBackEndActionGeneratorInstance()->loadShowView(
                compact('supplierQualification')
            );
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
