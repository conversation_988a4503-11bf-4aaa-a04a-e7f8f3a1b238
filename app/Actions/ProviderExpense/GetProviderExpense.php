<?php

namespace App\Actions\ProviderExpense;

use App\Enums\ProviderExpensePaymentMethodEnum;
use App\Models\FinanceParameter;
use App\Models\Permission;
use App\Models\Provider;
use App\Models\ProviderExpense;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetProviderExpense
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_PROVIDER_EXPENSES);
    }

    public function handle(ProviderExpense $providerExpense): Factory|View
    {
        return $providerExpense->getBackEndActionGeneratorInstance()->loadShowView([
            'providerExpense' => $providerExpense,
            'providers' => Provider::getForDropdown(),
            'financeParameter' => FinanceParameter::query()->first(),
            'paymentMethods' => ProviderExpensePaymentMethodEnum::getTranslated()
        ]);
    }
}
