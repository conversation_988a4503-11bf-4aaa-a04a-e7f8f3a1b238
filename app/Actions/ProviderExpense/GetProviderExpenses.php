<?php

namespace App\Actions\ProviderExpense;

use App\Models\Permission;
use App\Models\ProviderExpense;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetProviderExpenses
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_PROVIDER_EXPENSES);
    }

    public function handle(): mixed
    {
        return ProviderExpense::getBackEndActionGenerator()->loadIndexView();
    }
}
