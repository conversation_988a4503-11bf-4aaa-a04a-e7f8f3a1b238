<?php

namespace App\Actions\ProviderQualification;

use App\Models\Permission;
use App\Models\ProviderQualification;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GetProviderQualification
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_PROVIDER_QUALIFICATIONS);
    }

    public function handle(ProviderQualification $providerQualification): Factory|View
    {
        try {
            // Load the qualification with all related data
            $providerQualification->load([
                'provider',
                'providerQualificationItems.providerQualificationCriterion'
            ]);

            return $providerQualification->getBackEndActionGeneratorInstance()->loadShowView(
                compact('providerQualification')
            );
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
