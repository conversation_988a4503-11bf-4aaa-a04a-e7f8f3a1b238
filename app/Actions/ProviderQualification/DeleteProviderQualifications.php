<?php

namespace App\Actions\ProviderQualification;

use App\Core\Http\Requests\DeleteBatchRequest;
use App\Models\Permission;
use App\Models\ProviderQualification;
use Illuminate\Http\RedirectResponse;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteProviderQualifications
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::DELETE_PROVIDER_QUALIFICATIONS);
    }

    public function asController(DeleteBatchRequest $request): RedirectResponse
    {
        try {
            $this->handle($request->getDeleteIdsArray());
            return delete_batch_redirect_success('provider_qualifications.index');
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $ids)
    {
        try {
            collect($ids)->each(function (string $id): void {
                DeleteProviderQualification::run(ProviderQualification::find($id));
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
