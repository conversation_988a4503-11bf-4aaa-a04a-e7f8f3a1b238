<?php

namespace App\Actions\ProviderQualification;

use App\Models\Permission;
use App\Models\ProviderQualification;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetProviderQualifications
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_PROVIDER_QUALIFICATIONS);
    }

    public function handle():Factory|View
    {
        return ProviderQualification::getBackEndActionGenerator()->loadIndexView();
    }
}
