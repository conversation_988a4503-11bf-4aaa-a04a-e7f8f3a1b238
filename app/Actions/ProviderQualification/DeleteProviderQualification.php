<?php

namespace App\Actions\ProviderQualification;

use App\Models\ProviderQualification;
use App\Models\ProviderQualificationLog;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteProviderQualification
{
    use AsAction;

    public function handle(ProviderQualification $providerQualification)
    {
        try {
            DB::transaction(function () use ($providerQualification) {
                ProviderQualificationLog::create([
                    'provider_qualification_id' => $providerQualification->id,
                    'operator_id' => auth()->id(),
                    'action' => ProviderQualificationLog::ACTION_DELETED,
                    'data' => [
                        'provider_id' => $providerQualification->provider_id,
                        'grade' => $providerQualification->grade,
                        'qualified_at' => $providerQualification->qualified_at,
                        'qualification_expires_at' => $providerQualification->qualification_expires_at,
                        'deleted_at' => now(),
                    ],
                ]);

                $providerQualification->providerQualificationItems()->delete();
                $providerQualification->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
