<?php

namespace App\Actions\ProviderQualification;

use App\Models\Permission;
use App\Models\Provider;
use App\Models\ProviderQualification;
use App\Models\ProviderQualificationCriterion;
use App\Models\ProviderQualificationItem;
use App\Models\ProviderQualificationLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateProviderQualification
{
    use AsAction;

    protected array $rules = [
        'provider_id' => 'required|exists:providers,id',
        'qualified_at' => 'required|date',
        'qualification_expires_at' => 'required|date|after:qualified_at',
        'criteria' => 'required|array',
        'criteria.*.criterion_id' => 'required|exists:provider_qualification_criteria,id',
        'criteria.*.grade' => 'required|numeric',
    ];

    protected array $messages = [
        'provider_id.required' => 'É obrigatório selecionar um credenciado.',
        'provider_id.exists' => 'O credenciado selecionado não existe.',
        'qualified_at.required' => 'É obrigatório informar a data de qualificação.',
        'qualified_at.date' => 'A data de qualificação deve ser uma data válida.',
        'qualification_expires_at.required' => 'É obrigatório informar a data de expiração.',
        'qualification_expires_at.date' => 'A data de expiração deve ser uma data válida.',
        'qualification_expires_at.after' => 'A data de expiração deve ser posterior à data de qualificação.',
        'criteria.required' => 'É obrigatório informar as notas para todos os critérios.',
        'criteria.array' => 'Os critérios devem ser um array.',
        'criteria.*.criterion_id.required' => 'É obrigatório informar o critério.',
        'criteria.*.criterion_id.exists' => 'O critério informado não existe.',
        'criteria.*.grade.required' => 'É obrigatório informar a nota.',
        'criteria.*.grade.numeric' => 'A nota deve ser um número.',
    ];

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::CREATE_PROVIDER_QUALIFICATIONS);
    }

    public function asController(ActionRequest $request): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return ProviderQualification::getBackendActionGenerator()->loadCreateView([
                'providers' => Provider::getForDropdown(),
                'criteria' => ProviderQualificationCriterion::orderBy('sequence')->get(),
            ]);
        }

        $validator = Validator::make($request->all(), $this->rules, $this->messages);

        // Add custom validation for grade ranges
        $validator->after(function ($validator) use ($request) {
            if ($request->has('criteria')) {
                foreach ($request->input('criteria', []) as $index => $criteriaData) {
                    if (isset($criteriaData['criterion_id']) && isset($criteriaData['grade'])) {
                        $criterion = ProviderQualificationCriterion::find($criteriaData['criterion_id']);
                        if ($criterion) {
                            $grade = (float) $criteriaData['grade'];
                            if ($grade < $criterion->min_grade || $grade > $criterion->max_grade) {
                                $validator->errors()->add(
                                    "criteria.{$index}.grade",
                                    "A nota deve estar entre {$criterion->min_grade} e {$criterion->max_grade}."
                                );
                            }
                        }
                    }
                }
            }
        });

        $validator->validate();

        try {
            $providerQualification = $this->handle($validator->validated());
            return redirect_success('provider_qualifications.index', __('provider_qualifications.responses.create.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $data): ProviderQualification
    {
        try {
            return DB::transaction(function () use ($data) {
                // Calculate final grade based on weighted criteria
                $finalGrade = $this->calculateFinalGrade($data['criteria']);

                // Create the provider qualification
                $providerQualification = ProviderQualification::create([
                    'provider_id' => $data['provider_id'],
                    'grade' => $finalGrade,
                    'qualified_at' => $data['qualified_at'],
                    'qualification_expires_at' => $data['qualification_expires_at'],
                ]);

                // Create qualification items for each criterion
                foreach ($data['criteria'] as $criteriaData) {
                    $criterion = ProviderQualificationCriterion::find($criteriaData['criterion_id']);

                    ProviderQualificationItem::create([
                        'provider_qualification_id' => $providerQualification->id,
                        'provider_qualification_criterion_id' => $criteriaData['criterion_id'],
                        'sequence' => $criterion->sequence,
                        'grade' => $criteriaData['grade'],
                    ]);
                }

                // Log the creation
                ProviderQualificationLog::create([
                    'provider_qualification_id' => $providerQualification->id,
                    'operator_id' => auth()->id(),
                    'action' => ProviderQualificationLog::ACTION_CREATED,
                    'data' => [
                        'provider_id' => $providerQualification->provider_id,
                        'grade' => $providerQualification->grade,
                        'qualified_at' => $providerQualification->qualified_at,
                        'qualification_expires_at' => $providerQualification->qualification_expires_at,
                        'criteria_count' => count($data['criteria']),
                    ],
                ]);

                return $providerQualification;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }

    private function calculateFinalGrade(array $criteriaData): float
    {
        $totalWeightedGrade = 0;
        $totalWeight = 0;

        foreach ($criteriaData as $data) {
            $criterion = ProviderQualificationCriterion::find($data['criterion_id']);
            $grade = (float) $data['grade'];

            // Calculate weighted grade (grade * weight)
            $weightedGrade = $grade * $criterion->weight;
            $totalWeightedGrade += $weightedGrade;
            $totalWeight += $criterion->weight;
        }

        // Return the weighted average
        return $totalWeight > 0 ? round($totalWeightedGrade / $totalWeight, 2) : 0;
    }
}
