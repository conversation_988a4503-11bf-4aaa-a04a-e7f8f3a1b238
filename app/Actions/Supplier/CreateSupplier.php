<?php

namespace App\Actions\Supplier;

use App\Jobs\SendSupplierToERPFlex;
use App\Models\Log;
use App\Models\Permission;
use App\Models\Role;
use App\Models\Supplier;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateSupplier
{
    use AsAction;

    public array $rules = [
        'name' => 'required',
        'trading_name' => 'required',
        'email' => 'nullable',
        'tax_id_number' => 'nullable',
        'state_registration_no' => 'nullable',
        'city_registration_no' => 'nullable',
        'zipcode' => 'nullable',
        'address' => 'nullable',
        'number' => 'nullable',
        'additional_info' => 'nullable',
        'district' => 'nullable',
        'city' => 'nullable',
        'state' => 'nullable',
        'country' => 'nullable',
        'phone_1' => 'nullable',
        'phone_2' => 'nullable',
        'phone_3' => 'nullable',
        'system' => 'nullable',
        'suframa' => 'nullable',
        'gln' => 'nullable',
        'import_document' => 'nullable',
        'national_simple_tax' => 'nullable',
        'qualified' => 'nullable',
        'qualification_expires_at' => 'nullable',
        'preferred_earning_method' => 'nullable',
        'bank_code' => 'nullable',
        'bank_id' => 'nullable',
        'branch_number' => 'nullable',
        'branch_digit' => 'nullable',
        'account_number' => 'nullable',
        'account_digit' => 'nullable',
        'pix_1' => 'nullable',
        'pix_2' => 'nullable',
        'pix_3' => 'nullable',
    ];

    public array $messages = [
        'name.required' => 'É obrigatório informar a razão social.',
        'trading_name.required' => 'É obrigatório informar o nome fantasia.',
    ];

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::CREATE_SUPPLIERS);
    }

    public function asController(ActionRequest $request): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return Supplier::getBackEndActionGenerator()->loadCreateView();
        }

        $validator = Validator::make($request->all(), $this->rules, $this->messages);
        $validator->validate();

        try {
            $this->handle($validator->validated());
            return redirect_success('suppliers.index', __('suppliers.responses.create.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $data): Supplier
    {
        try {
            return DB::transaction(function () use ($data) {
                $user = (new User())
                    ->fill($data)
                    ->setUsernameForType(Supplier::class);

                $user->save();

                $data['user_id'] = $user->id;

                /** @var \App\Models\Supplier $supplier */
                $supplier = Supplier::create($data);

                $user->assignRole(Role::SUPPLIER);

                Log::logEntityCreation(
                    $supplier->id,
                    Supplier::class,
                    json_decode($supplier->toJson())
                );

                tenant()->run(fn () => SendSupplierToERPFlex::dispatch($supplier)->onQueue(
                    config('queue.default_queue_names.data_send')
                ));

                return $supplier;
            });
        } catch (Throwable $th) {
            throwUnknownError($th);
        }
    }
}
