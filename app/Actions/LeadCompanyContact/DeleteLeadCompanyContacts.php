<?php

namespace App\Actions\LeadCompanyContact;

use App\Core\Http\Requests\DeleteBatchRequest;
use App\Models\LeadCompany;
use App\Models\LeadCompanyContact;
use Illuminate\Http\RedirectResponse;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class DeleteLeadCompanyContacts
{
    use AsAction;

    /**
     * Handle the action as a controller.
     *
     * @param  \App\Core\Http\Requests\DeleteBatchRequest $request
     * @param  \App\Models\LeadCompany $leadCompany
     * @return \Illuminate\Http\RedirectResponse
     */
    public function asController(DeleteBatchRequest $request, LeadCompany $leadCompany): RedirectResponse
    {
        try {
            $this->handle($request->getDeleteIdsArray());

            return redirect_success(
                'lead_companies.show',
                __('lead_company_contacts.responses.delete.success'),
                $leadCompany->id,
            );
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    /**
     * Handle the action.
     *
     * @param  array $ids
     * @return void
     * @throws \Exception
     */
    public function handle(array $ids): void
    {
        try {
            collect($ids)->each(function (string $id): void {
                DeleteLeadCompanyContact::run(LeadCompanyContact::find($id));
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
