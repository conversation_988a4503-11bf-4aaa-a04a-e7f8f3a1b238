<?php

namespace App\Actions\LeadCompanyContact;

use App\Models\LeadCompany;
use App\Models\LeadCompanyContact;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateLeadCompanyContact
{
    use AsAction;

    /**
     * Get the validation rules that are applied to the request.
     *
     * @var array
     */
    protected array $rules = [
        'name' => 'required',
        'email' => 'nullable|email',
        'phone' => 'nullable',
        'job_title' => 'nullable',
        'main' => 'nullable',
    ];

    /**
     * Get the custom validation messages that are applied to the rules.
     *
     * @var array
     */
    protected array $messages = [
        'name.required' => 'É obrigatório informar o nome.',
        'email.email' => 'O e-mail deve ter um formato válido.',
    ];

    /**
     * Authorize the request.
     *
     * @param  \Lorisleiva\Actions\ActionRequest $request
     * @return bool
     */
    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::UPDATE_LEADS);
    }

    /**
     * Handle the action as a controller.
     *
     * @param  \Lorisleiva\Actions\ActionRequest $request
     * @param  \App\Models\LeadCompany $leadCompany
     * @return mixed
     * @throws \Illuminate\Validation\ValidationException
     */
    public function asController(ActionRequest $request, LeadCompany $leadCompany): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return LeadCompanyContact::getBackEndActionGenerator()->loadCreateView([
                'leadCompany' => $leadCompany,
            ]);
        }

        $request->merge([
            'main' => $request->has('main'),
        ]);

        $validator = Validator::make($request->all(), $this->rules, $this->messages);
        $validator->validate();

        try {
            $this->handle($leadCompany, $validator->validated());

            return redirect_success(
                'lead_companies.edit',
                __('lead_company_contacts.responses.create.success'),
                $leadCompany->id,
            );
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\LeadCompany $leadCompany
     * @param  array $data
     * @return \App\Models\LeadCompanyContact
     * @throws \Exception
     */
    public function handle(LeadCompany $leadCompany, array $data): LeadCompanyContact
    {
        try {
            return DB::transaction(function () use ($leadCompany, $data): LeadCompanyContact {
                /** @var \App\Models\LeadCompanyContact $leadCompanyContact */
                $leadCompanyContact = $leadCompany->leadCompanyContacts()->create($data);

                // If this contact is marked as main, unmark all other contacts as main
                if ($data['main'] ?? false) {
                    $leadCompany->leadCompanyContacts()
                        ->where('id', '!=', $leadCompanyContact->id)
                        ->update(['main' => false]);
                }

                return $leadCompanyContact;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
