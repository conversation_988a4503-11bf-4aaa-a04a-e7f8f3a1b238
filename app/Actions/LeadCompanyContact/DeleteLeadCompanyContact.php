<?php

namespace App\Actions\LeadCompanyContact;

use App\Models\LeadCompanyContact;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteLeadCompanyContact
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\LeadCompanyContact $leadCompanyContact
     * @return void
     * @throws \Exception
     */
    public function handle(LeadCompanyContact $leadCompanyContact): void
    {
        try {
            $leadCompanyContact->delete();
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
