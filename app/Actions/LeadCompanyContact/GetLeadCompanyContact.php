<?php

namespace App\Actions\LeadCompanyContact;

use App\Models\LeadCompany;
use App\Models\LeadCompanyContact;
use App\Models\Permission;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetLeadCompanyContact
{
    use AsAction;

    /**
     * Authorize the request.
     *
     * @param  \Lorisleiva\Actions\ActionRequest $request
     * @return bool
     */
    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::UPDATE_LEADS);
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\LeadCompany $leadCompany
     * @param  \App\Models\LeadCompanyContact $leadCompanyContact
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
     */
    public function handle(LeadCompany $leadCompany, LeadCompanyContact $leadCompanyContact): Factory|View|Application
    {
        return $leadCompanyContact->getBackEndActionGeneratorInstance()->loadShowView(
            compact('leadCompany', 'leadCompanyContact'),
        );
    }
}
