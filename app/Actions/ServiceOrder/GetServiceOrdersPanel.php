<?php

namespace App\Actions\ServiceOrder;

use App\Models\Contract;
use App\Models\Permission;
use App\Models\Schedule;
use App\Models\ServiceOrder;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetServiceOrdersPanel
{
    use AsAction;

    /**
     * Authorize the request.
     *
     * @param  \Lorisleiva\Actions\ActionRequest $request
     * @return bool
     */
    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_SERVICE_ORDERS);
    }

    /**
     * Handle the action.
     *
     * @param  \Lorisleiva\Actions\ActionRequest $request
     * @return mixed
     */
    public function handle(ActionRequest $request): mixed
    {
        if ($request->ajax()) {
            return ServiceOrder::query()
                ->with([
                    'contract.company',
                    'branch',
                    'procedure',
                    'scheduleServiceOrders.schedule.technician',
                    'schedulingUser',
                    'visitingUser',
                    'checklistReceivingUser',
                    'insertionUser',
                    'attendanceUser'
                ])
                ->where(function (Builder $query) {
                    return $query
                        ->whereIn('status', ServiceOrder::OPERATIONAL_STATUSES)
                        ->orWhere(function (Builder $query) {
                            return $query
                                ->where('status', ServiceOrder::STATUS_ATTENDED)
                                ->where('attended_at', '>=', now()->subDays(15)->format('Y-m-d'));
                        });
                })
                ->whereHas('contract', function (Builder $query): Builder {
                    return $query
                        ->where('status', '<>', Contract::STATUS_CANCELLED)
                        ->whereNull('deleted_at');
                })
                ->where(function (Builder $query) use ($request) {
                    return $query
                        ->where('id', $request->input('search'))
                        ->orWhereHas('contract.company', function (Builder $query) use ($request) {
                            return $query->where('name', 'like', '%' . $request->input('search') . '%');
                        })
                        ->orWhereHas('branch', function (Builder $query) use ($request) {
                            return $query->where('branch_name', 'like', '%' . $request->input('search') . '%');
                        })
                        ->orWhereHas('procedure', function (Builder $query) use ($request) {
                            return $query->where('name', 'like', '%' . $request->input('search') . '%');
                        });
                })
                ->when($request->filled('rework_filter'), function (Builder $query) use ($request) {
                    $reworkFilter = $request->input('rework_filter');

                    if ($reworkFilter === 'rework_only') {
                        return $query->where('rework', true);
                    } elseif ($reworkFilter === 'normal') {
                        return $query->where('rework', false);
                    }

                    return $query;
                })
                ->orderBy('created_at')
                ->get();
        }

        return view('app.engineering.service_orders.service_orders_panel', [
            'serviceOrders' => ServiceOrder::query()
                ->with([
                    'contract.company',
                    'branch',
                    'procedure',
                    'scheduleServiceOrders.schedule.technician',
                    'schedulingUser',
                    'visitingUser',
                    'checklistReceivingUser',
                    'insertionUser',
                    'attendanceUser'
                ])
                ->where(function (Builder $query): Builder {
                    return $query
                        ->whereIn('status', ServiceOrder::OPERATIONAL_STATUSES)
                        ->orWhere(function (Builder $query) {
                            return $query
                                ->where('status', ServiceOrder::STATUS_ATTENDED)
                                ->where('attended_at', '>=', now()->subDays(15)->format('Y-m-d'));
                        });
                })
                ->whereHas('contract', function (Builder $query): Builder {
                    return $query
                        ->where('status', '<>', Contract::STATUS_CANCELLED)
                        ->whereNull('deleted_at');
                })
                ->orderBy('created_at')
                ->get(),
            'schedules' => Schedule::with('technician:id,name')
                ->where('date', '>=', now()->format('Y-m-d'))
                ->where('morning_available', true)
                ->orWhere('afternoon_available', true)
                ->get()
                ->sortBy('date')
                ->sortBy('technician.name')
        ]);
    }
}
