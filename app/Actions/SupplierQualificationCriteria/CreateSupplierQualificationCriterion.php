<?php

namespace App\Actions\SupplierQualificationCriteria;

use App\Models\Permission;
use App\Models\SupplierQualificationCriterion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class CreateSupplierQualificationCriterion
{
    use AsAction;

    protected array $rules = [
        'name' => 'required',
        'sequence' => 'required',
        'min_grade' => 'required',
        'max_grade' => 'required',
        'step' => 'required',
        'weight' => 'required',
    ];

    protected array $messages = [
        'name.required' => 'É obrigatório informar o nome.',
        'sequence.required' => 'É obrigatório informar a sequência.',
        'min_grade.required' => 'É obrigatório informar a nota mínima.',
        'max_grade.required' => 'É obrigatório informar a nota máxima.',
        'step.required' => 'É obrigatório informar o passo.',
        'weight.required' => 'É obrigatório informar o peso.',
    ];

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::CREATE_SUPPLIER_QUALIFICATION_CRITERIA);
    }

    public function asController(ActionRequest $request): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return SupplierQualificationCriterion::getBackendActionGenerator()->loadCreateView();
        }

        $validator = Validator::make($request->all(), $this->rules, $this->messages);
        $validator->validate();

        try {
            $this->handle($validator->validated());
            return redirect_success('supplier_qualification_criteria.index', __('supplier_qualification_criteria.responses.create.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $data): SupplierQualificationCriterion
    {
        try {
            return SupplierQualificationCriterion::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
