<?php

namespace App\Actions\SupplierQualificationCriteria;

use App\Models\SupplierQualificationCriterion;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteSupplierQualificationCriterion
{
    use AsAction;

    public function handle(SupplierQualificationCriterion $supplierQualificationCriterion)
    {
        try {
            $supplierQualificationCriterion->delete();
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
