<?php

namespace App\Actions\SupplierQualificationCriteria;

use App\Models\Permission;
use App\Models\SupplierQualificationCriterion;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class GetSupplierQualificationCriterion
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_SUPPLIER_QUALIFICATION_CRITERIA);
    }

    public function handle(SupplierQualificationCriterion $supplierQualificationCriterion): Factory|View
    {
        try {
            return $supplierQualificationCriterion->getBackEndActionGeneratorInstance()->loadShowView(
                compact('supplierQualificationCriterion')
            );
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
