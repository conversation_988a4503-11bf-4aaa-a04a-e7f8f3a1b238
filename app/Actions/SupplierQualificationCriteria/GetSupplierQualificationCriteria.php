<?php

namespace App\Actions\SupplierQualificationCriteria;

use App\Models\Permission;
use App\Models\SupplierQualificationCriterion;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetSupplierQualificationCriteria
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_SUPPLIER_QUALIFICATION_CRITERIA);
    }

    public function handle(): Factory|View
    {
        return SupplierQualificationCriterion::getBackEndActionGenerator()->loadIndexView();
    }
}
