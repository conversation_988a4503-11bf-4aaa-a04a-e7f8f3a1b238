<?php

namespace App\Actions\SupplierQualificationCriteria;

use App\Core\Http\Requests\DeleteBatchRequest;
use App\Models\Permission;
use App\Models\SupplierQualificationCriterion;
use Illuminate\Http\RedirectResponse;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class DeleteSupplierQualificationCriteria
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::DELETE_SUPPLIER_QUALIFICATION_CRITERIA);
    }

    public function asController(DeleteBatchRequest $request): RedirectResponse
    {
        try {
            $this->handle($request->getDeleteIdsArray());
            return delete_batch_redirect_success('supplier_qualification_criteria.index');
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $ids)
    {
        try {
            collect($ids)->each(function (string $id): void {
                DeleteSupplierQualificationCriterion::run(SupplierQualificationCriterion::find($id));
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
