<?php

namespace App\Http\Livewire\LeadCompanyContact\Tables;

use App\Core\Http\Livewire\LaravelLivewireTables\BaseLivewireTable;
use App\Models\LeadCompanyContact;
use Illuminate\Database\Eloquent\Builder;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;

class LeadCompanyContactsTable extends BaseLivewireTable
{
    public int $leadCompanyId;
    protected string $resourceRoute = 'lead_company_contacts';

    /**
     * Mount the component.
     *
     * @param  int $leadCompanyId
     * @param  bool $manage
     * @return void
     */
    public function mount(int $leadCompanyId, bool $manage = true): void
    {
        $this->leadCompanyId = $leadCompanyId;
        $this->manage = $manage;
    }

    /**
     * @inheritDoc
     */
    public function builder(): Builder
    {
        return LeadCompanyContact::query()
            ->where('lead_company_id', $this->leadCompanyId)
            ->orderBy('lead_company_contacts.name');
    }

    /**
     * @inheritDoc
     */
    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->searchable(),
            Column::make(__('lead_company_contacts.forms.fields.name'), 'name')
                ->sortable()
                ->searchable(),
            Column::make(__('lead_company_contacts.forms.fields.email'), 'email')
                ->sortable()
                ->searchable(),
            Column::make(__('lead_company_contacts.forms.fields.phone'), 'phone')
                ->sortable()
                ->searchable(),
            Column::make(__('lead_company_contacts.forms.fields.job_title'), 'job_title')
                ->sortable()
                ->searchable(),
            BooleanColumn::make(__('lead_company_contacts.forms.fields.main'), 'main')
                ->sortable(),
            $this->getDefaultButtonLinkColumn(
                manage: $this->manage,
                chainModelIds: [$this->leadCompanyId],
            ),
        ];
    }

    /**
     * @inheritDoc
     */
    public function openDeleteModal(): void
    {
        $this->emit('deleteModalOpened', [
            'route' => route("$this->resourceRoute.delete_batch", $this->leadCompanyId),
            'deleteIds' => implode(',', $this->getSelected())
        ]);
    }
}
