<?php

namespace App\Http\Livewire\ProviderQualificationCriteria\Tables;

use App\Core\Http\Livewire\LaravelLivewireTables\BaseLivewireTable;
use Rappasoft\LaravelLivewireTables\Views\Column;
use App\Models\ProviderQualificationCriterion;

class ProviderQualificationCriteriaTable extends BaseLivewireTable
{
    protected $model = ProviderQualificationCriterion::class;
    protected string $resourceRoute = 'provider_qualification_criteria';

    public function columns(): array
    {
        return [
            Column::make(__('provider_qualification_criteria.forms.fields.id'), "id")
                ->hideIf(true),
            Column::make(__('provider_qualification_criteria.forms.fields.name'), "name")
                ->sortable()
                ->searchable(),
            Column::make(__('provider_qualification_criteria.forms.fields.sequence'), "sequence")
                ->sortable(),
            Column::make(__('provider_qualification_criteria.forms.fields.min_grade'), "min_grade")
                ->sortable(),
            Column::make(__('provider_qualification_criteria.forms.fields.max_grade'), "max_grade")
                ->sortable(),
            Column::make(__('provider_qualification_criteria.forms.fields.step'), "step")
                ->sortable(),
            Column::make(__('provider_qualification_criteria.forms.fields.weight'), "weight")
                ->sortable(),
            $this->getDefaultButtonLinkColumn(),
        ];
    }
}
