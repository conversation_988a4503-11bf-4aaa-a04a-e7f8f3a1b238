<?php

namespace App\Http\Livewire\ProviderQualification\Tables;

use App\Core\Http\Livewire\LaravelLivewireTables\BaseLivewireTable;
use App\Models\ProviderQualification;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Illuminate\Database\Eloquent\Builder;

class ProviderQualificationsTable extends BaseLivewireTable
{
    protected $model = ProviderQualification::class;
    protected string $resourceRoute = 'provider_qualifications';

    public function builder(): Builder
    {
        return ProviderQualification::query()
            ->with(['provider:id,name,trading_name,tax_id_number']);
    }

    public function columns(): array
    {
        return [
            Column::make(__('provider_qualifications.forms.fields.id'), 'id')
                ->hideIf(true),
            Column::make(__('provider_qualifications.forms.fields.provider_id'), 'provider.name')
                ->sortable()
                ->searchable(),
            Column::make(__('provider_qualifications.forms.fields.provider_trading_name'), 'provider.trading_name')
                ->sortable()
                ->searchable(),
            Column::make(__('provider_qualifications.forms.fields.provider_tax_id_number'), 'provider.tax_id_number')
                ->sortable()
                ->searchable()
                ->format(fn(string $value): string => mask_cnpj($value)),
            Column::make(__('provider_qualifications.forms.fields.grade'), 'grade')
                ->sortable()
                ->format(function ($value) {
                    return number_format($value, 2, ',', '.');
                }),
            Column::make(__('provider_qualifications.forms.fields.qualified_at'), 'qualified_at')
                ->sortable()
                ->format(fn(string $value, ProviderQualification $row): string => $row->friendly_qualified_at),
            Column::make(__('provider_qualifications.forms.fields.qualification_expires_at'), 'qualification_expires_at')
                ->sortable()
                ->format(fn(string $value, ProviderQualification $row): string => $row->friendly_qualification_expires_at),
            Column::make(__('provider_qualifications.forms.fields.created_at'), 'created_at')
                ->sortable()
                ->format(fn(string $value, ProviderQualification $row): string => $row->friendly_created_at),
            $this->getDefaultButtonLinkColumn(edit: false),
        ];
    }

    /**
     * Open the delete modal.
     *
     * @return void
     */
    public function openDeleteModal()
    {
        $this->emit('deleteModalOpened', [
            'route' => route("$this->resourceRoute.delete_batch"),
            'deleteIds' => implode(',', $this->getSelected())
        ]);
    }
}
