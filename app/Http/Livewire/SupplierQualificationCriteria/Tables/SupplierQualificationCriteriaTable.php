<?php

namespace App\Http\Livewire\SupplierQualificationCriteria\Tables;

use App\Core\Http\Livewire\LaravelLivewireTables\BaseLivewireTable;
use App\Models\SupplierQualificationCriterion;
use Rappasoft\LaravelLivewireTables\Views\Column;

class SupplierQualificationCriteriaTable extends BaseLivewireTable
{
    protected $model = SupplierQualificationCriterion::class;
    protected string $resourceRoute = 'supplier_qualification_criteria';

    public function columns(): array
    {
        return [
            Column::make(__('supplier_qualification_criteria.forms.fields.id'), "id")
                ->hideIf(true),
            Column::make(__('supplier_qualification_criteria.forms.fields.name'), "name")
                ->sortable()
                ->searchable(),
            Column::make(__('supplier_qualification_criteria.forms.fields.sequence'), "sequence")
                ->sortable(),
            Column::make(__('supplier_qualification_criteria.forms.fields.min_grade'), "min_grade")
                ->sortable(),
            Column::make(__('supplier_qualification_criteria.forms.fields.max_grade'), "max_grade")
                ->sortable(),
            Column::make(__('supplier_qualification_criteria.forms.fields.step'), "step")
                ->sortable(),
            Column::make(__('supplier_qualification_criteria.forms.fields.weight'), "weight")
                ->sortable(),
            $this->getDefaultButtonLinkColumn(),
        ];
    }

    /**
     * Open the delete modal.
     *
     * @return void
     */
    public function openDeleteModal()
    {
        $this->emit('deleteModalOpened', [
            'route' => route("$this->resourceRoute.delete_batch"),
            'deleteIds' => implode(',', $this->getSelected())
        ]);
    }
}
