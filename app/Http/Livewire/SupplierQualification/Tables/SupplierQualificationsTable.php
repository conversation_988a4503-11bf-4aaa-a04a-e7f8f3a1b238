<?php

namespace App\Http\Livewire\SupplierQualification\Tables;

use App\Core\Http\Livewire\LaravelLivewireTables\BaseLivewireTable;
use App\Models\SupplierQualification;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Illuminate\Database\Eloquent\Builder;

class SupplierQualificationsTable extends BaseLivewireTable
{
    protected $model = SupplierQualification::class;
    protected string $resourceRoute = 'supplier_qualifications';

    public function builder(): Builder
    {
        return SupplierQualification::query()
            ->with(['supplier']);
    }

    public function columns(): array
    {
        return [
            Column::make(__('supplier_qualifications.forms.fields.id'), 'id')
                ->hideIf(true),
            Column::make(__('supplier_qualifications.forms.fields.supplier_id'), 'supplier.name')
                ->sortable()
                ->searchable(),
            Column::make(__('supplier_qualifications.forms.fields.supplier_trading_name'), 'supplier.trading_name')
                ->sortable()
                ->searchable(),
            Column::make(__('supplier_qualifications.forms.fields.supplier_tax_id_number'), 'supplier.tax_id_number')
                ->sortable()
                ->searchable()
                ->format(fn(?string $value): ?string => mask_cnpj($value)),
            Column::make(__('supplier_qualifications.forms.fields.grade'), 'grade')
                ->sortable()
                ->format(function ($value) {
                    return number_format($value, 2, ',', '.');
                }),
            Column::make(__('supplier_qualifications.forms.fields.qualified_at'), 'qualified_at')
                ->sortable()
                ->format(fn(?string $value, SupplierQualification $row): ?string => $row->friendly_qualified_at),
            Column::make(__('supplier_qualifications.forms.fields.qualification_expires_at'), 'qualification_expires_at')
                ->sortable()
                ->format(fn(?string $value, SupplierQualification $row): ?string => $row->friendly_qualification_expires_at),
            Column::make(__('supplier_qualifications.forms.fields.created_at'), 'created_at')
                ->sortable()
                ->format(fn(?string $value, SupplierQualification $row): ?string => $row->friendly_created_at),
            $this->getDefaultButtonLinkColumn(edit: false),
        ];
    }

    /**
     * Open the delete modal.
     *
     * @return void
     */
    public function openDeleteModal()
    {
        $this->emit('deleteModalOpened', [
            'route' => route("$this->resourceRoute.delete_batch"),
            'deleteIds' => implode(',', $this->getSelected())
        ]);
    }
}
