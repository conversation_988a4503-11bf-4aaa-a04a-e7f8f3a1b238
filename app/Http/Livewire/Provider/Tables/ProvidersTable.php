<?php

namespace App\Http\Livewire\Provider\Tables;

use App\Core\Http\Livewire\LaravelLivewireTables\BaseLivewireTable;
use App\Core\Http\Livewire\LaravelLivewireTables\ButtonLinkColumn;
use Rappasoft\LaravelLivewireTables\Views\Column;
use App\Models\Provider;
use App\Models\ProviderContact;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use <PERSON>pasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;
use Rappasoft\LaravelLivewireTables\Views\Columns\ButtonGroupColumn;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;

class ProvidersTable extends BaseLivewireTable
{
    protected $model = Provider::class;
    protected string $resourceRoute = 'providers';

    public array $cities;
    public array $states;

    protected $listeners = [
        'stateUpdated' => 'updateCities',
    ];

    public function mount(array $cities, array $states): void
    {
        $this->cities = $cities;
        $this->states = $states;
    }

    /** @inheritDoc */
    public function configure(): void
    {
        parent::configure();

        $this->setDefaultSort('name');

        $this->activatable = true;
    }

    public function columns(): array
    {
        return [
            Column::make(__('providers.forms.fields.id'), 'id')
                ->sortable()
                ->searchable(),
            Column::make(__('providers.forms.fields.name'), 'name')
                ->sortable()
                ->searchable(),
            Column::make(__('providers.forms.fields.trading_name'), 'trading_name')
                ->sortable()
                ->searchable(),
            Column::make(__('providers.forms.fields.tax_id_number'), 'tax_id_number')
                ->sortable()
                ->searchable(function (Builder $query, string $searchTerm): Builder {
                    return $query->orWhere('tax_id_number', 'like', '%' . Str::remove(['.', '-', '/'], $searchTerm) . '%');
                })
                ->format(fn(?string $value, Provider $row): string => $row->friendly_tax_id_number),
            Column::make('E-mails', 'email')
                ->sortable()
                ->format(function (?string $value, Provider $row): ?string {
                    return $row->providerContacts->implode(function (ProviderContact $providerContact) {
                        return $providerContact->email;
                    }, ', ');
                })
                ->searchable(function (Builder $query, string $searchTerm): Builder {
                    return $query->orWhereHas('providerContacts', function (Builder $query) use ($searchTerm) {
                        return $query->where('email', 'like', "%$searchTerm%");
                    });
                }),
            Column::make(__('providers.forms.fields.city'), 'city')
                ->sortable(),
            BooleanColumn::make(__('providers.forms.fields.active'), 'active'),
            ButtonGroupColumn::make('Ações')
                ->buttons([
                    ButtonLinkColumn::make('Ver')
                        ->title(function ($row): string {
                            return '';
                        })
                        ->location(fn($row): string => route("$this->resourceRoute.show", $row))
                        ->renderHtml(function ($row): mixed {
                            $extraButtons = "
                                <button type='button'
                                    class='btn btn-sm shadow-sm btn-outline-secondary mr-2 datatable-procedures-button'
                                    title='Tabela de procedimentos'
                                    wire:click='openProceduresModal($row->id)'>
                                    <i class='fa fa-dollar-sign'></i>
                                </button>
                            ";

                            return $this->getActionsColumnButtons(
                                $row,
                                $this->resourceRoute,
                                true,
                                true,
                                overridePermission: false,
                                extraButtons: $extraButtons,
                                columnWidth: 130
                            );
                        })
                ])
        ];
    }

    public function filters(): array
    {
        return [
            SelectFilter::make('Cidade', 'city')
                ->setFilterPillTitle('Cidade')
                ->options(array_merge([
                    '' => 'Todos',
                ], $this->cities))
                ->filter(function (Builder $builder, string $value): Builder {
                    return $builder->where('city', $value);
                }),
            SelectFilter::make('Estado', 'state')
                ->setFilterPillTitle('Estado')
                ->options(array_merge([
                    'all' => 'Todos',
                ], $this->states))
                ->filter(function (Builder $builder, string $value): Builder {
                    $this->emit('stateUpdated', $value);
                    return $builder->where('state', $value);
                }),
            SelectFilter::make('Ativo', 'active')
                ->setFilterPillTitle('Ativo')
                ->setFilterDefaultValue(true)
                ->options([
                    '' => 'Todos',
                    true => 'Sim',
                    false => 'Não',
                ])
                ->filter(function (Builder $builder, string $value): Builder {
                    return $builder->where('active', (int) $value);
                }),
            SelectFilter::make('Integrado com ERPFlex', 'integrated_with_erp_Flex')
                ->setFilterPillTitle('Integrado com ERPFlex')
                ->setFilterDefaultValue(true)
                ->options([
                    '' => 'Todos',
                    true => 'Somente integrados',
                    false => 'Somente não integrados',
                ])
                ->filter(function (Builder $builder, string $value): Builder {
                    return $builder
                        ->when(((int) $value) === 1, fn(Builder $query): Builder => $query->whereNotNull('erp_flex_id'))
                        ->when(((int) $value) === 0, fn(Builder $query): Builder => $query->whereNull('erp_flex_id'));
                }),
        ];
    }

    public function updateCities(string $state)
    {
        if ($state !== 'all') {
            $this->cities = Provider::query()
                ->select(['city'])
                ->distinct()
                ->where('state', $state)
                ->orderBy('city')
                ->get()
                ->mapWithKeys(fn(Provider $provider) => [$provider->city => $provider->city])
                ->toArray();

            return;
        }

        $this->cities = Provider::query()
            ->select(['city'])
            ->distinct()
            ->orderBy('city')
            ->get()
            ->mapWithKeys(fn(Provider $provider) => [$provider->city => $provider->city])
            ->toArray();
    }

    public function openProceduresModal(int $providerId)
    {
        $this->emit('proceduresModalOpened', $providerId);
    }
}
