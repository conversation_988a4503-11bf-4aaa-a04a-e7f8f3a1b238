<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Attributes\GetsFormattedAmountAttribute;
use App\Models\Concerns\ProviderExpense\HandlesProviderExpenseAttributes;
use App\Models\Concerns\ProviderExpense\HandlesProviderExpenseRelationships;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * Provider expense model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $operator_id
 * @property  int $provider_id
 * @property  string $erp_flex_id
 * @property  \Carbon\Carbon $issued_at
 * @property  \Carbon\Carbon $entered_at
 * @property  string $document_type
 * @property  string $document_number
 * @property  string $additional_info
 * @property  string $expense_description
 * @property  float $procedures_calculated_amount
 * @property  float $procedures_informed_amount
 * @property  float $procedures_difference_amount
 * @property  string $procedures_difference_additional_info
 * @property  float $gross_amount
 * @property  float $ir_wht_amount
 * @property  string $ir_difference_reason
 * @property  float $pcc_wht_amount
 * @property  string $pcc_difference_reason
 * @property  float $iss_wht_amount
 * @property  string $iss_difference_reason
 * @property  float $others_wht_amount
 * @property  string $others_difference_reason
 * @property  float $net_amount
 * @property  int $installments
 * @property  string $status
 * @property  string $payment_method
 * @property  string $bank_name
 * @property  string $bank_branch_number
 * @property  string $bank_branch_digit
 * @property  string $bank_account_number
 * @property  string $bank_account_digit
 * @property  string $pix_key
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_issued_at
 * @property  string $friendly_entered_at
 *
 * @property  \App\Models\Operator $operator
 * @property  \App\Models\Provider $provider
 * @property  \App\Models\ExpenseType $expenseType
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderExpenseFile[] $providerExpenseFiles
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderExpenseProcedure[] $providerExpenseProcedures
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderExpenseItem[] $providerExpenseItems
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderExpenseInstallment[] $providerExpenseInstallments
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderExpenseAdvance[] $providerExpenseAdvances
 */
class ProviderExpense extends Model
{
    use GetsFormattedAmountAttribute;
    use HandlesProviderExpenseAttributes;
    use HandlesProviderExpenseRelationships;
    use HasFactory;

    public const HTML_ENTITY = 'provider-expense';
    public const MODULE = Module::ACCREDITATION;
    public const RESOURCE_ROUTE = 'provider_expenses';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'operator_id',
        'provider_id',
        'expense_type_id',
        'erp_flex_id',
        'issued_at',
        'entered_at',
        'document_type',
        'document_number',
        'additional_info',
        'expense_description',
        'procedures_calculated_amount',
        'procedures_informed_amount',
        'procedures_difference_amount',
        'procedures_difference_additional_info',
        'gross_amount',
        'ir_wht_amount',
        'ir_difference_reason',
        'pcc_wht_amount',
        'pcc_difference_reason',
        'iss_wht_amount',
        'iss_difference_reason',
        'others_wht_amount',
        'others_difference_reason',
        'added_amount',
        'discounted_amount',
        'net_amount',
        'installments',
        'status',
        'payment_method',
        'bank_name',
        'bank_branch_number',
        'bank_branch_digit',
        'bank_account_number',
        'bank_account_digit',
        'pix_key',
        'approved_at',
        'rejected_at',
        'rejection_reason'
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted(): void
    {
        static::creating(function (ProviderExpense $providerExpense) {
            $providerExpense->operator_id = auth()->user()->operator->id;
        });

        static::deleting(function (self $providerExpense) {
            $providerExpense->loadUnloadedRelations([
                'providerExpenseFiles',
                'providerExpenseProcedures',
                'providerExpenseItems',
                'providerExpenseInstallments',
                'providerExpenseAdvances',
            ]);

            $providerExpense->providerExpenseProcedures->each(
                fn (ProviderExpenseProcedure $providerExpenseProcedure) => $providerExpenseProcedure->delete()
            );

            $providerExpense->providerExpenseItems->each(
                fn (ProviderExpenseItem $providerExpenseItem) => $providerExpenseItem->delete()
            );

            $providerExpense->providerExpenseInstallments->each(
                fn (ProviderExpenseInstallment $providerExpenseInstallment) => $providerExpenseInstallment->delete()
            );

            $providerExpense->providerExpenseFiles->each(function (ProviderExpenseFile $providerExpenseFile) {
                Storage::delete("$providerExpenseFile->path/$providerExpenseFile->filename");

                $providerExpenseFile->delete();
            });

            if ($providerExpense->providerExpenseAdvances->count() > 0) {
                ProviderAdvance::find($providerExpense->providerExpenseAdvances[0]->provider_advance_id)->update([
                    'used' => false
                ]);
            }

            $providerExpense->providerExpenseAdvances->each(
                fn (ProviderExpenseAdvance $providerExpenseAdvance) => $providerExpenseAdvance->delete()
            );
        });
    }

    /**
     * Get all provider expenses for DataTable displaying.
     *
     * @param  array $operatorIds
     * @param  string|null $search
     * @param  string|null $status
     * @param  string|null $enteredAtFrom
     * @param  string|null $enteredAtTo
     * @return mixed
     */
    public static function getForDataTable(
        array $operatorIds,
        ?string $search = null,
        ?string $status = null,
        ?string $enteredAtFrom = null,
        ?string $enteredAtTo = null
    ): mixed {
        return self::select(['id', 'operator_id', 'provider_id', 'document_number', 'entered_at', 'issued_at', 'gross_amount', 'net_amount', 'status', 'erp_flex_id'])
            ->with([
                'operator:id,name',
                'provider:id,name,tax_id_number',
                'providerExpenseFiles',
            ])
            ->when(
                !is_null($search),
                fn ($q) => $q->where(
                    fn ($q) => $q
                        ->where('id', $search)
                        ->orWhere('document_number', $search)
                        ->orWhereHas(
                            'provider',
                            fn ($q) => $q->where(
                                fn ($q) => $q
                                    ->where('name', 'like', "%$search%")
                                    ->orWhere('tax_id_number', 'like', '%' . Str::remove(['/', '-', '.'], $search) . '%')
                            )
                        )
                        ->orWhereHas('operator', fn ($q) => $q->where('name', 'like', "%$search%"))
                        ->orWhere('gross_amount', unmask_money($search))
                        ->orWhere('net_amount', unmask_money($search))
                )
            )
            ->when(!is_null($enteredAtFrom), fn ($q) => $q->whereDate('entered_at', '>=', carbon($enteredAtFrom)->format('Y-m-d')))
            ->when(!is_null($enteredAtTo), fn ($q) => $q->whereDate('entered_at', '<=', carbon($enteredAtTo)->format('Y-m-d')))
            ->when(!is_null($status), fn ($q) => $q->where('status', $status))
            ->orderByDesc('id');
    }

    /**
     * Get all provider expenses for auditing DataTable displaying.
     *
     * @return mixed
     */
    public static function getForAuditingDataTable(): mixed
    {
        return self::query()
            ->select(['id', 'operator_id', 'provider_id', 'document_number', 'issued_at', 'entered_at', 'procedures_calculated_amount', 'procedures_informed_amount', 'procedures_difference_amount', 'status'])
            ->with(['operator:id,name', 'provider:id,name'])
            ->where('procedures_difference_amount', '>', 5)
            ->orderByDesc('id');
    }
}
