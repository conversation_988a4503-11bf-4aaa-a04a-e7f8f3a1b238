<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Concerns\SupplierQualification\HandlesSupplierQualificationAttributes;
use App\Models\Concerns\SupplierQualification\HandlesSupplierQualificationRelationships;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Supplier qualification model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $supplier_id
 * @property  float $grade
 * @property  \Carbon\Carbon $qualified_at
 * @property  \Carbon\Carbon $qualification_expires_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  string $friendly_qualified_at
 * @property  string $friendly_qualification_expires_at
 * @property  string $friendly_created_at
 * @property  string $friendly_updated_at
 *
 * @property  \App\Models\Supplier $supplier
 *
 * @property  \Illuminate\Support\Collection|\App\Models\SupplierQualificationItem[] $supplierQualificationItems
 */
class SupplierQualification extends Model
{
    use HandlesSupplierQualificationAttributes;
    use HandlesSupplierQualificationRelationships;
    use SoftDeletes;

    public const MODULE = Module::PROVIDING;
    public const RESOURCE_ROUTE = 'supplier_qualifications';

    protected $fillable = [
        'supplier_id',
        'grade',
        'qualified_at',
        'qualification_expires_at',
    ];
}
