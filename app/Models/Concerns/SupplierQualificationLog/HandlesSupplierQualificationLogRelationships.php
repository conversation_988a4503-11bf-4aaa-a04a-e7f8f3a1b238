<?php

namespace App\Models\Concerns\SupplierQualificationLog;

use App\Models\Operator;
use App\Models\SupplierQualification;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesSupplierQualificationLogRelationships
{
    public function supplierQualification(): BelongsTo
    {
        return $this->belongsTo(SupplierQualification::class);
    }

    public function operator(): BelongsTo
    {
        return $this->belongsTo(Operator::class);
    }
}
