<?php

namespace App\Models\Concerns\User;

use App\Models\Concerns\User\HandlesEngineeringModule;
use App\Models\Permission;
use App\Models\Role;

trait VerifiesUserAuthorizations
{
    use HandlesEngineeringModule;

    public function viewsManagementAccessControlModule(): bool
    {
        return $this->can(Permission::GET_ROLES)
            || $this->can(Permission::GET_OPERATORS);
    }

    public function viewsManagementModule(): bool
    {
        return $this->can(Permission::GET_COMPANIES)
            || $this->can(Permission::GET_HOLIDAYS)
            || $this->can(Permission::GET_INDICES)
            || $this->can(Permission::GET_PROCEDURES)
            || $this->can(Permission::GET_EXPENSE_TYPES)
            || $this->can(Permission::GET_CONTRACT_TYPES)
            || $this->can(Permission::GET_REASONS)
            || $this->can(Permission::GET_WORKFLOWS)
            || $this->can(Permission::GET_SCHEDULES);
    }

    public function viewsAnyAccreditationModule(): bool
    {
        return $this->can(Permission::GET_PROVIDERS)
            || $this->can(Permission::GET_PROVIDER_EXPENSES);
    }

    public function viewsAnyProvidingModule(): bool
    {
        return $this->can(Permission::GET_SUPPLIERS)
            || $this->can(Permission::GET_SUPPLIER_EXPENSES);
    }

    public function viewsAnyManagementModule(): bool
    {
        return $this->viewsManagementAccessControlModule()
            || $this->viewsManagementModule();
    }

    public function viewsAnyContractModule(): bool
    {
        return $this->can(Permission::GET_CONTRACTS);
    }

    public function viewsAnyCrmModule(): bool
    {
        return $this->hasRole(Role::ADMINISTRATOR)
            || $this->roles->first()->shows_in_salesman_dropdowns;
    }

    public function viewsAnyAfterSalesModule(): bool
    {
        return $this->hasRole(Role::ADMINISTRATOR)
            || $this->roles->first()->shows_in_after_sales_dropdowns;
    }

    public function viewsAnyOperationModule(): bool
    {
        return $this->can(Permission::GET_EXAMS)
            || $this->can(Permission::GET_COMPANY_CLOSING_GLOSSES)
            || $this->can(Permission::GET_SERVICE_ORDERS);
    }

    public function viewsAnyKnowledgeBaseModule(): bool
    {
        return true;
    }

    public function viewsAnyTicketsModule(): bool
    {
        return true;
    }

    public function viewsRecurrentBillingModule(): bool
    {
        return $this->can(Permission::GET_COMPANY_CLOSINGS);
    }

    public function viewsStandAloneBillingModule(): bool
    {
        return $this->can(Permission::GET_STAND_ALONE_CONTRACTS_FOR_BILLING);
    }

    public function viewsAnyBillingModule(): bool
    {
        return $this->viewsRecurrentBillingModule()
            || $this->viewsStandAloneBillingModule();
    }

    public function viewsAnyFinanceModule(): bool
    {
        return $this->viewsReceivablesModule();
    }

    public function viewsReceivablesModule(): bool
    {
        return $this->can(Permission::GET_RECEIVABLES);
    }

    public function viewsAnyReportModule(): bool
    {
        return true;
    }

    public function viewsAnyImportModule(): bool
    {
        return $this->hasRole(Role::ADMINISTRATOR);
    }

    public function viewsParametersModule(): bool
    {
        return $this->hasRole(Role::ADMINISTRATOR);
    }

    public function viewsAuditingModule(): bool
    {
        return $this->hasRole(Role::ADMINISTRATOR);
    }

    public function viewsLogsModule(): bool
    {
        return $this->hasRole(Role::ADMINISTRATOR);
    }

    public function viewsIntegrationsModule(): bool
    {
        return $this->hasRole(Role::ADMINISTRATOR);
    }
}
