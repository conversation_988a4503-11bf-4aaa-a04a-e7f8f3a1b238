<?php

namespace App\Models\Concerns\SupplierQualification;

use App\Models\Supplier;
use App\Models\SupplierQualificationItem;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesSupplierQualificationRelationships
{
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function supplierQualificationItems(): HasMany
    {
        return $this->hasMany(SupplierQualificationItem::class);
    }
}
