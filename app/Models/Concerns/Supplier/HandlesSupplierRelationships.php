<?php

namespace App\Models\Concerns\Supplier;

use App\Models\Bank;
use App\Models\Operator;
use App\Models\SupplierExpense;
use App\Models\SupplierQualification;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesSupplierRelationships
{
    /**
     * Load the bank relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bank(): BelongsTo
    {
        return $this->belongsTo(Bank::class);
    }

    /**
     * Load the creation operator relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function creationOperator(): BelongsTo
    {
        return $this->belongsTo(Operator::class, 'created_by_operator_id');
    }

    /**
     * Load the supplier expenses relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function supplierExpenses(): HasMany
    {
        return $this->hasMany(SupplierExpense::class);
    }

    /**
     * Load the supplier qualifications relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function supplierQualifications(): HasMany
    {
        return $this->hasMany(SupplierQualification::class);
    }
}
