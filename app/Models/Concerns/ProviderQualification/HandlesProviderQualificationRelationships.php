<?php

namespace App\Models\Concerns\ProviderQualification;

use App\Models\Provider;
use App\Models\ProviderQualificationItem;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesProviderQualificationRelationships
{
    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class);
    }

    public function providerQualificationItems(): HasMany
    {
        return $this->hasMany(ProviderQualificationItem::class);
    }
}
