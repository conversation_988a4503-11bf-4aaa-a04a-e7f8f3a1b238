<?php

namespace App\Models\Concerns\ProviderQualification;

trait HandlesProviderQualificationAttributes
{
    public function getFriendlyQualifiedAtAttribute(): string
    {
        return format_date($this->qualified_at);
    }

    public function getFriendlyQualificationExpiresAtAttribute(): string
    {
        return format_date($this->qualification_expires_at);
    }

    public function getFriendlyCreatedAtAttribute(): string
    {
        return format_datetime($this->created_at);
    }

    public function getFriendlyUpdatedAtAttribute(): string
    {
        return format_datetime($this->updated_at);
    }
}
