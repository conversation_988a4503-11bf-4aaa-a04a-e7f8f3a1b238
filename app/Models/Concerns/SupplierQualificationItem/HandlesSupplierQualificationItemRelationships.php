<?php

namespace App\Models\Concerns\SupplierQualificationItem;

use App\Models\SupplierQualification;
use App\Models\SupplierQualificationCriterion;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesSupplierQualificationItemRelationships
{
    public function supplierQualification(): BelongsTo
    {
        return $this->belongsTo(SupplierQualification::class);
    }

    public function supplierQualificationCriterion(): BelongsTo
    {
        return $this->belongsTo(SupplierQualificationCriterion::class);
    }
}
