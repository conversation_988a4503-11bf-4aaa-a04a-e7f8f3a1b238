<?php

namespace App\Models\Concerns\ProviderQualificationItem;

use App\Models\ProviderQualification;
use App\Models\ProviderQualificationCriterion;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesProviderQualificationItemRelationships
{
    public function providerQualification(): BelongsTo
    {
        return $this->belongsTo(ProviderQualification::class);
    }

    public function providerQualificationCriterion(): BelongsTo
    {
        return $this->belongsTo(ProviderQualificationCriterion::class);
    }
}
