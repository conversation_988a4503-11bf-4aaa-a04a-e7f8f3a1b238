<?php

namespace App\Models\Concerns\Provider;

use App\Models\Bank;
use App\Models\Operator;
use App\Models\Procedure;
use App\Models\ProviderCityCoverageCity;
use App\Models\ProviderCompany;
use App\Models\ProviderContact;
use App\Models\ProviderExpense;
use App\Models\ProviderExpenseType;
use App\Models\ProviderProcedure;
use App\Models\ProviderQualification;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

trait HandlesProviderRelationships
{
    /**
     * Load the user relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Load the bank relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bank(): BelongsTo
    {
        return $this->belongsTo(Bank::class);
    }

    /**
     * Load the creation operator relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function creationOperator(): BelongsTo
    {
        return $this->belongsTo(Operator::class, 'created_by_operator_id');
    }

    /**
     * Load the provider expense types relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providerExpenseTypes(): HasMany
    {
        return $this->hasMany(ProviderExpenseType::class);
    }

    /**
     * Load the provider procedures relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providerProcedures(): HasMany
    {
        return $this->hasMany(ProviderProcedure::class);
    }

    /**
     * Load the provider contacts relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providerContacts(): HasMany
    {
        return $this->hasMany(ProviderContact::class);
    }

    /**
     * Load the procedures relationship through an intermediary table.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function procedures(): HasManyThrough
    {
        return $this->hasManyThrough(Procedure::class, ProviderProcedure::class);
    }

    /**
     * Load the provider companies relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providerCompanies(): HasMany
    {
        return $this->hasMany(ProviderCompany::class);
    }

    /**
     * Load the provider city coverage cities relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providerCityCoverageCities(): HasMany
    {
        return $this->hasMany(ProviderCityCoverageCity::class);
    }

    /**
     * Load the provider expenses relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providerExpenses(): HasMany
    {
        return $this->hasMany(ProviderExpense::class);
    }

    /**
     * Load the provider qualifications relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providerQualifications(): HasMany
    {
        return $this->hasMany(ProviderQualification::class);
    }
}
