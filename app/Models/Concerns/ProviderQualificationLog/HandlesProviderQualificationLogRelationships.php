<?php

namespace App\Models\Concerns\ProviderQualificationLog;

use App\Models\Operator;
use App\Models\ProviderQualification;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesProviderQualificationLogRelationships
{
    /**
     * Load the provider qualification relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function providerQualification(): BelongsTo
    {
        return $this->belongsTo(ProviderQualification::class);
    }

    /**
     * Load the operator relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(Operator::class);
    }
}
