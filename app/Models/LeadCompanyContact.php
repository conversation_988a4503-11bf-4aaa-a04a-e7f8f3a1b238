<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Concerns\LeadCompanyContact\HandlesLeadCompanyContactRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Lead company contact model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $lead_company_id
 * @property  string $name
 * @property  string $email
 * @property  string $phone
 * @property  string $job_title
 * @property  bool $main
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\LeadCompany $leadCompany
 */
class LeadCompanyContact extends Model
{
    use HandlesLeadCompanyContactRelationships;
    use HasFactory;

    public const MODULE = Module::CRM;
    public const RESOURCE_ROUTE = 'lead_company_contacts';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'lead_company_id',
        'name',
        'email',
        'phone',
        'job_title',
        'main',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'main' => 'boolean',
    ];
}
