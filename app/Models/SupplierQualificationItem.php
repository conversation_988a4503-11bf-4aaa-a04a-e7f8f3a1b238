<?php

namespace App\Models;

use App\Models\Concerns\SupplierQualificationItem\HandlesSupplierQualificationItemRelationships;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Supplier qualification item model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $supplier_qualification_id
 * @property  int $supplier_qualification_criterion_id
 * @property  int $sequence
 * @property  float $grade
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  \App\Models\SupplierQualification $supplierQualification
 * @property  \App\Models\SupplierQualificationCriterion $supplierQualificationCriterion
 */
class SupplierQualificationItem extends Model
{
    use HandlesSupplierQualificationItemRelationships;
    use SoftDeletes;

    protected $fillable = [
        'supplier_qualification_id',
        'supplier_qualification_criterion_id',
        'sequence',
        'grade',
    ];
}
