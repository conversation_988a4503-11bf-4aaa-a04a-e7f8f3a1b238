<?php

namespace App\Models;

use App\Models\Concerns\ProviderQualificationLog\HandlesProviderQualificationLogRelationships;

/**
 * Provider qualification log model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $provider_qualification_id
 * @property  int $operator_id
 * @property  string $action
 * @property  array $data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ProviderQualification $providerQualification
 * @property  \App\Models\Operator $operator
 */
class ProviderQualificationLog extends Model
{
    use HandlesProviderQualificationLogRelationships;

    protected $fillable = [
        'provider_qualification_id',
        'operator_id',
        'action',
        'data',
    ];

    protected $casts = [
        'data' => 'array',
    ];

    public const ACTION_CREATED = 'created';
    public const ACTION_DELETED = 'deleted';
}
