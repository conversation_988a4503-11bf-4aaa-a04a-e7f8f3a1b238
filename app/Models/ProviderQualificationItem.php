<?php

namespace App\Models;

use App\Models\Concerns\ProviderQualificationItem\HandlesProviderQualificationItemRelationships;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Provider qualification item model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $provider_qualification_id
 * @property  int $provider_qualification_criterion_id
 * @property  int $sequence
 * @property  float $grade
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  \App\Models\ProviderQualification $providerQualification
 * @property  \App\Models\ProviderQualificationCriterion $providerQualificationCriterion
 */
class ProviderQualificationItem extends Model
{
    use HandlesProviderQualificationItemRelationships;
    use SoftDeletes;

    protected $fillable = [
        'provider_qualification_id',
        'provider_qualification_criterion_id',
        'sequence',
        'grade',
    ];
}
