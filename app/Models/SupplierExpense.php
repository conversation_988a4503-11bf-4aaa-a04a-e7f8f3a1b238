<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Attributes\GetsFormattedAmountAttribute;
use App\Models\Concerns\SupplierExpense\HandlesSupplierExpenseAttributes;
use App\Models\Concerns\SupplierExpense\HandlesSupplierExpenseRelationships;
use App\Models\Relationships\HasManySupplierExpenseInstallments;
use App\Models\Relationships\HasManySupplierExpenseItems;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

/**
 * Supplier expense model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $operator_id
 * @property  int $supplier_id
 * @property  string $erp_flex_id
 * @property  \Carbon\Carbon $issued_at
 * @property  \Carbon\Carbon $entered_at
 * @property  string $document_type
 * @property  string $document_number
 * @property  string $additional_info
 * @property  string $expense_description
 * @property  float $gross_amount
 * @property  float $ir_wht_amount
 * @property  string $ir_difference_reason
 * @property  float $pcc_wht_amount
 * @property  string $pcc_difference_reason
 * @property  float $iss_wht_amount
 * @property  string $iss_difference_reason
 * @property  float $others_wht_amount
 * @property  string $others_difference_reason
 * @property  float $net_amount
 * @property  int $installments
 * @property  string $status
 * @property  string $payment_method
 * @property  string $bank_name
 * @property  string $bank_branch_number
 * @property  string $bank_branch_digit
 * @property  string $bank_account_number
 * @property  string $bank_account_digit
 * @property  string $pix_key
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_entered_at
 *
 * @property  \App\Models\Operator $operator
 * @property  \App\Models\Supplier $supplier
 *
 * @property  \App\Models\SupplierExpenseFile[]|\Illuminate\Support\Collection $supplierExpenseFiles
 * @property  \App\Models\SupplierExpenseItem[]|\Illuminate\Support\Collection $supplierExpenseItems
 * @property  \App\Models\SupplierExpenseInstallment[]|\Illuminate\Support\Collection $supplierExpenseInstallments
 * @property  \App\Models\ServiceOrder[]|\Illuminate\Support\Collection $serviceOrders
 */
class SupplierExpense extends Model
{
    use GetsFormattedAmountAttribute;
    use HandlesSupplierExpenseAttributes;
    use HandlesSupplierExpenseRelationships;
    use HasFactory;
    use HasManySupplierExpenseItems;
    use HasManySupplierExpenseInstallments;

    public const HTML_ENTITY = 'supplier-expense';
    public const MODULE = Module::PROVIDING;
    public const RESOURCE_ROUTE = 'supplier_expenses';

    public const DOCUMENT_TYPE_DV = 'DV';
    public const DOCUMENT_TYPE_FM = 'FM';
    public const DOCUMENT_TYPE_AF = 'AF';

    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    public const PAYMENT_METHOD_CASH = 'cash';
    public const PAYMENT_METHOD_DEPOSIT = 'deposit';
    public const PAYMENT_METHOD_PIX = 'pix';
    public const PAYMENT_METHOD_CREDIT_CARD = 'credit-card';
    public const PAYMENT_METHOD_BANK_SLIP = 'bank-slip';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'operator_id',
        'supplier_id',
        'expense_type_id',
        'erp_flex_id',
        'issued_at',
        'entered_at',
        'document_type',
        'document_number',
        'additional_info',
        'expense_description',
        'gross_amount',
        'ir_wht_amount',
        'ir_difference_reason',
        'pcc_wht_amount',
        'pcc_difference_reason',
        'iss_wht_amount',
        'iss_difference_reason',
        'others_wht_amount',
        'others_difference_reason',
        'added_amount',
        'discounted_amount',
        'net_amount',
        'installments',
        'status',
        'payment_method',
        'bank_name',
        'bank_branch_number',
        'bank_branch_digit',
        'bank_account_number',
        'bank_account_digit',
        'pix_key',
        'approved_at',
        'rejected_at',
        'rejection_reason'
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function (SupplierExpense $supplierExpense) {
            $supplierExpense->operator_id = auth()->user()->operator->id;
        });

        static::deleting(function (SupplierExpense $supplierExpense) {
            $supplierExpense->loadUnloadedRelations([
                'supplierExpenseFiles',
                'supplierExpenseItems',
                'supplierExpenseInstallments',
                'supplierExpenseAdvances',
            ]);

            $supplierExpense->supplierExpenseItems->each(fn (SupplierExpenseItem $supplierExpenseItem) => $supplierExpenseItem->delete());

            $supplierExpense->supplierExpenseInstallments->each(fn (SupplierExpenseInstallment $supplierExpenseInstallment) => $supplierExpenseInstallment->delete());

            $supplierExpense->supplierExpenseFiles->each(function (SupplierExpenseFile $supplierExpenseFile) {
                Storage::delete("$supplierExpenseFile->path/$supplierExpenseFile->filename");

                $supplierExpenseFile->delete();
            });

            if ($supplierExpense->supplierExpenseAdvances->count() > 0) {
                SupplierAdvance::find($supplierExpense->supplierExpenseAdvances[0]->supplier_advance_id)->update([
                    'used' => false
                ]);
            }

            $supplierExpense->supplierExpenseAdvances->each(
                fn (SupplierExpenseAdvance $supplierExpenseAdvance) => $supplierExpenseAdvance->delete()
            );
        });
    }

    /**
     * Get all provider expenses for DataTable displaying.
     *
     * @param  array $operatorIds
     * @param  string|null $search
     * @param  string|null $status
     * @param  string|null $enteredAtFrom
     * @param  string|null $enteredAtTo
     * @return mixed
     */
    public static function getForDataTable(
        array $operatorIds,
        ?string $search = null,
        ?string $status = null,
        ?string $enteredAtFrom = null,
        ?string $enteredAtTo = null,
    ): mixed {
        return self::select(['id', 'operator_id', 'supplier_id', 'document_number', 'entered_at', 'issued_at', 'gross_amount', 'net_amount', 'status', 'erp_flex_id'])
            ->with([
                'operator:id,name',
                'supplier:id,name',
                'supplierExpenseFiles',
            ])
            ->when(!auth()->user()->hasRole(Role::ADMINISTRATOR), fn ($q) => $q->whereIn('operator_id', $operatorIds))
            ->when(
                !is_null($search),
                fn ($q) => $q->where(
                    fn ($q) => $q
                        ->where('id', $search)
                        ->orWhere('document_number', $search)
                        ->orWhereHas('supplier', fn ($q) => $q->where('name', 'like', "%$search%"))
                        ->orWhereHas('operator', fn ($q) => $q->where('name', 'like', "%$search%"))
                        ->orWhere('gross_amount', $search)
                        ->orWhere('net_amount', $search)
                )
            )
            ->when(!is_null($enteredAtFrom), fn ($q) => $q->whereDate('entered_at', '>=', carbon($enteredAtFrom)->format('Y-m-d')))
            ->when(!is_null($enteredAtTo), fn ($q) => $q->whereDate('entered_at', '<=', carbon($enteredAtTo)->format('Y-m-d')))
            ->when(!is_null($status), fn ($q) => $q->where('status', $status))
            ->orderByDesc('id');
    }
}
