<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Attributes\SetsNameAttribute;
use App\Models\Concerns\GetsForDropdown;
use App\Models\Concerns\Provider\HandlesProviderAttributes;
use App\Models\Concerns\Provider\HandlesProviderRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * Provider model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $user_id
 * @property  string $erp_flex_id
 * @property  string $soc_id
 * @property  string $clm_online_id
 * @property  string $name
 * @property  string $trading_name
 * @property  string $email
 * @property  string $tax_id_number
 * @property  string $state_registration_no
 * @property  string $city_registration_no
 * @property  string $zipcode
 * @property  string $address
 * @property  string $number
 * @property  string $additional_info
 * @property  string $district
 * @property  string $city
 * @property  string $state
 * @property  string $country
 * @property  string $phone_1
 * @property  string $phone_2
 * @property  string $phone_3
 * @property  bool $national_simple_tax
 * @property  bool $qualified
 * @property  \Carbon\Carbon $qualification_expires_at
 * @property  string $preferred_earning_method
 * @property  int $payment_day
 * @property  int $payment_term
 * @property  int $week_expiration_day
 * @property  int $bank_id
 * @property  string $bank_code
 * @property  string $bank_name
 * @property  string $branch_number
 * @property  string $branch_digit
 * @property  string $account_number
 * @property  string $account_digit
 * @property  string $pix_1
 * @property  string $pix_2
 * @property  string $pix_3
 * @property  string $treatment_type
 * @property  \Carbon\Carbon $monday_starting_time
 * @property  \Carbon\Carbon $monday_ending_time
 * @property  \Carbon\Carbon $tuesday_starting_time
 * @property  \Carbon\Carbon $tuesday_ending_time
 * @property  \Carbon\Carbon $wednesday_starting_time
 * @property  \Carbon\Carbon $wednesday_ending_time
 * @property  \Carbon\Carbon $thursday_starting_time
 * @property  \Carbon\Carbon $thursday_ending_time
 * @property  \Carbon\Carbon $friday_starting_time
 * @property  \Carbon\Carbon $friday_ending_time
 * @property  \Carbon\Carbon $saturday_starting_time
 * @property  \Carbon\Carbon $saturday_ending_time
 * @property  string $treatment_additional_info
 * @property  bool $advance_payment
 * @property  bool $is_schedule_managed
 * @property  bool $active
 * @property  int $created_by_operator_id
 * @property  \Carbon\Carbon $service_provision_started_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  string $friendly_tax_id_number
 * @property  string $friendly_preferred_earning_method
 * @property  string $friendly_treatment_type
 * @property  string $friendly_service_provision_started_at
 *
 * @property  \App\Models\User $user
 * @property  \App\Models\Bank $bank
 * @property  \App\Models\Operator $creationOperator
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderExpenseType[] $providerExpenseTypes
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderExpense[] $providerExpenses
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderProcedure[] $providerProcedures
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderContact[] $providerContacts
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderCompany[] $providerCompanies
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderCityCoverageCity[] $providerCityCoverageCities
 */
class Provider extends BaseModel
{
    use HandlesProviderAttributes;
    use HandlesProviderRelationships;
    use GetsForDropdown;
    use HasFactory;
    use SetsNameAttribute;
    use SoftDeletes;

    public const MODULE = Module::ACCREDITATION;
    public const RESOURCE_ROUTE = 'providers';
    public const INACTIVATABLE = true;

    public const TREATMENT_TYPE_ARRIVAL_ORDER = 'arrival-order';
    public const TREATMENT_TYPE_APPOINTMENT = 'appointment';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'erp_flex_id',
        'soc_id',
        'clm_online_id',
        'name',
        'trading_name',
        'email',
        'tax_id_number',
        'state_registration_no',
        'city_registration_no',
        'zipcode',
        'address',
        'number',
        'additional_info',
        'district',
        'city',
        'state',
        'country',
        'phone_1',
        'phone_2',
        'phone_3',
        'national_simple_tax',
        'qualified',
        'qualification_expires_at',
        'preferred_earning_method',
        'payment_day',
        'payment_term',
        'week_expiration_day',
        'bank_id',
        'bank_code',
        'bank_name',
        'branch_number',
        'branch_digit',
        'account_number',
        'account_digit',
        'pix_1',
        'pix_2',
        'pix_3',
        'treatment_type',
        'monday_starting_time',
        'monday_ending_time',
        'tuesday_starting_time',
        'tuesday_ending_time',
        'wednesday_starting_time',
        'wednesday_ending_time',
        'thursday_starting_time',
        'thursday_ending_time',
        'friday_starting_time',
        'friday_ending_time',
        'saturday_starting_time',
        'saturday_ending_time',
        'treatment_additional_info',
        'advance_payment',
        'is_schedule_managed',
        'active',
        'service_provision_started_at',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'active' => 'bool',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'friendly_tax_id_number',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        parent::booted();

        static::creating(function (self $provider) {
            if (!auth()->check()) {
                return;
            }

            $provider->created_by_operator_id = auth()->user()->operator->id;

            $clmOnlineIntegrationExists = ClmOnlineIntegration::query()
                ->where('active', true)
                ->exists();

            if (!$clmOnlineIntegrationExists) {
                return;
            }

            $provider->clm_online_id = $provider->getNextClmOnlineCustomerId();
        });

        static::saving(function (self $provider): void {
            $provider->state_registration_no ??= 'ISENTO';
            $provider->city_registration_no ??= '.';
        });
    }

    /**
     * Get the next CLM Online customer ID.
     *
     * @return string
     */
    public function getNextClmOnlineCustomerId(): string
    {
        return self::query()
            ->select([DB::raw('cast(clm_online_id as unsigned integer) as id')])
            ->orderByRaw('id desc')
            ->first()
            ->id + 1;
    }
}
