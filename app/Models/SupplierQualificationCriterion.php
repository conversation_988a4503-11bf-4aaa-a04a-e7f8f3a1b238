<?php

namespace App\Models;

use App\Core\Module;

/**
 * Supplier qualification criterion model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  string $type
 * @property  int $sequence
 * @property  float $min_grade
 * @property  float $max_grade
 * @property  float $step
 * @property  float $weight
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_type
 */
class SupplierQualificationCriterion extends Model
{
    public const MODULE = Module::PROVIDING;
    public const RESOURCE_ROUTE = 'supplier_qualification_criteria';

    protected $fillable = [
        'name',
        'sequence',
        'min_grade',
        'max_grade',
        'step',
        'weight',
    ];
}
