<?php

namespace App\Models;

use App\Models\Concerns\SupplierQualificationLog\HandlesSupplierQualificationLogRelationships;

/**
 * Supplier qualification log model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $supplier_qualification_id
 * @property  int $operator_id
 * @property  string $action
 * @property  array|null $data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\SupplierQualification $supplierQualification
 * @property  \App\Models\Operator $operator
 */
class SupplierQualificationLog extends Model
{
    use HandlesSupplierQualificationLogRelationships;

    protected $fillable = [
        'supplier_qualification_id',
        'operator_id',
        'action',
        'data',
    ];

    protected $casts = [
        'data' => 'array',
    ];

    public const ACTION_CREATED = 'created';
    public const ACTION_DELETED = 'deleted';
}
