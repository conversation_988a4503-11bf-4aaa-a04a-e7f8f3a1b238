<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Attributes\SetsNameAttribute;
use App\Models\Concerns\GetsForDropdown;
use App\Models\Concerns\Supplier\HandlesSupplierAttributes;
use App\Models\Concerns\Supplier\HandlesSupplierRelationships;
use App\Models\Relationships\BelongsToUser;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Supplier model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $user_id
 * @property  string $erp_flex_id
 * @property  string $name
 * @property  string $trading_name
 * @property  string $email
 * @property  string $tax_id_number
 * @property  string $state_registration_no
 * @property  string $city_registration_no
 * @property  string $zipcode
 * @property  string $address
 * @property  string $number
 * @property  string $additional_info
 * @property  string $district
 * @property  string $city
 * @property  string $state
 * @property  string $country
 * @property  string $phone_1
 * @property  string $phone_2
 * @property  string $phone_3
 * @property  string $system
 * @property  string $suframa
 * @property  string $gln
 * @property  string $import_document
 * @property  bool $national_simple_tax
 * @property  bool $qualified
 * @property  \Carbon\Carbon $qualification_expires_at
 * @property  string $preferred_earning_method
 * @property  int $bank_id
 * @property  string $bank_code
 * @property  string $bank_name
 * @property  string $branch_number
 * @property  string $branch_digit
 * @property  string $account_number
 * @property  string $account_digit
 * @property  string $pix_1
 * @property  string $pix_2
 * @property  string $pix_3
 * @property  int $created_by_operator_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \DateTime $deleted_at
 *
 * @property  string $friendly_tax_id_number
 * @property  string $friendly_preferred_earning_method
 *
 * @property  \App\Models\Bank $bank
 * @property  \App\Models\Operator $creationOperator
 *
 * @property  \Illuminate\Support\Collection|\App\Models\SupplierExpense[] $supplierExpenses
 */
class Supplier extends Model
{
    use BelongsToUser;
    use GetsForDropdown;
    use HandlesSupplierAttributes;
    use HandlesSupplierRelationships;
    use HasFactory;
    use SetsNameAttribute;
    use SoftDeletes;

    public const HTML_ENTITY = 'supplier';
    public const MODULE = Module::PROVIDING;
    public const RESOURCE_ROUTE = 'suppliers';
    public const INACTIVATABLE = true;

    public const PREFERRED_EARNING_METHOD_CASH = 'cash';
    public const PREFERRED_EARNING_METHOD_TRANSFER = 'transfer';
    public const PREFERRED_EARNING_METHOD_PIX = 'pix';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'activity_id',
        'erp_flex_id',
        'name',
        'trading_name',
        'email',
        'tax_id_number',
        'state_registration_no',
        'city_registration_no',
        'zipcode',
        'address',
        'number',
        'additional_info',
        'district',
        'city',
        'state',
        'country',
        'phone_1',
        'phone_2',
        'phone_3',
        'system',
        'suframa',
        'gln',
        'import_document',
        'national_simple_tax',
        'qualified',
        'qualification_expires_at',
        'preferred_earning_method',
        'bank_id',
        'bank_code',
        'bank_name',
        'branch_number',
        'branch_digit',
        'account_number',
        'account_digit',
        'pix_1',
        'pix_2',
        'pix_3',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'friendly_tax_id_number',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function (self $supplier) {
            if (!auth()->check()) {
                return;
            }

            $supplier->created_by_operator_id = auth()->user()->operator->id;
        });

        static::deleted(fn (self $supplier) => $supplier->user->delete());
    }

    /**
     * Get all suppliers for DataTable displaying.
     *
     * @param  string|null $search
     * @param  bool|null $active
     * @param  bool|null $integratedWithErpFlex
     * @return mixed
     */
    public static function getForDataTable(?string $search = null, ?bool $active = null, ?bool $integratedWithErpFlex = null): mixed
    {
        return self::select(['id', 'user_id', 'name', 'trading_name', 'email', 'tax_id_number', 'erp_flex_id'])
            ->with(['user:id,active'])
            ->when(
                !is_null($search),
                fn ($q) => $q
                    ->where('name', 'like', "%$search%")
                    ->orWhere('trading_name', 'like', "%$search%")
                    ->orWhere(
                        fn ($query) => is_numeric($search)
                            ? $query->where('tax_id_number', 'like', '%' . unmask_cnpj($search) . '%')
                            : $query
                    )
                    ->orWhere('email', 'like', "%$search%")
            )
            ->when(
                !is_null($active),
                fn ($q) => $q
                    ->when($active, fn ($q) => $q->whereHas('user', fn ($q) => $q->where('active', true)))
                    ->when(!$active, fn ($q) => $q->whereHas('user', fn ($q) => $q->where('active', false)))
            )
            ->when(
                !is_null($integratedWithErpFlex),
                fn ($q) => $q
                    ->when($integratedWithErpFlex, fn ($q) => $q->whereNotNull('erp_flex_id'))
                    ->when(!$integratedWithErpFlex, fn ($q) => $q->whereNull('erp_flex_id'))
            );
    }
}
