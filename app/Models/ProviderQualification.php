<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Concerns\ProviderQualification\HandlesProviderQualificationAttributes;
use App\Models\Concerns\ProviderQualification\HandlesProviderQualificationRelationships;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Provider qualification model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $provider_id
 * @property  float $grade
 * @property  \Carbon\Carbon $qualified_at
 * @property  \Carbon\Carbon $qualification_expires_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  string $friendly_qualified_at
 * @property  string $friendly_qualification_expires_at
 * @property  string $friendly_created_at
 * @property  string $friendly_updated_at
 *
 * @property  \App\Models\Provider $provider
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderQualificationItem[] $providerQualificationItems
 */
class ProviderQualification extends Model
{
    use HandlesProviderQualificationAttributes;
    use HandlesProviderQualificationRelationships;
    use SoftDeletes;

    public const MODULE = Module::ACCREDITATION;
    public const RESOURCE_ROUTE = 'provider_qualifications';

    protected $fillable = [
        'provider_id',
        'grade',
        'qualified_at',
        'qualification_expires_at',
    ];
}
